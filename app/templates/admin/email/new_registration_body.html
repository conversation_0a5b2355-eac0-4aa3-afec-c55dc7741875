<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>New Trial Registration</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f4f4f4;
    }

    .email-container {
      background-color: #ffffff;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .header {
      background-color: #2c3e50;
      color: #ffffff;
      padding: 30px;
      text-align: center;
    }

    .header h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 300;
    }

    .content {
      padding: 40px 30px;
    }

    .alert-badge {
      background-color: #e74c3c;
      color: white;
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: bold;
      text-transform: uppercase;
      display: inline-block;
      margin-bottom: 20px;
    }

    .user-info {
      background-color: #f8f9fa;
      border-left: 4px solid #3498db;
      padding: 20px;
      margin: 20px 0;
      border-radius: 0 4px 4px 0;
    }

    .info-row {
      display: flex;
      margin-bottom: 12px;
      align-items: center;
    }

    .info-label {
      font-weight: bold;
      color: #2c3e50;
      min-width: 120px;
      margin-right: 10px;
    }

    .info-value {
      color: #555;
    }

    .actions {
      margin-top: 30px;
      text-align: center;
    }

    .btn {
      display: inline-block;
      padding: 12px 24px;
      background-color: #3498db;
      color: white;
      text-decoration: none;
      border-radius: 4px;
      font-weight: bold;
      margin: 0 10px;
      transition: background-color 0.3s;
    }

    .btn:hover {
      background-color: #2980b9;
    }

    .btn-secondary {
      background-color: #95a5a6;
    }

    .btn-secondary:hover {
      background-color: #7f8c8d;
    }

    .footer {
      background-color: #ecf0f1;
      padding: 20px 30px;
      text-align: center;
      font-size: 12px;
      color: #7f8c8d;
    }

    .timestamp {
      font-style: italic;
      color: #7f8c8d;
      font-size: 14px;
    }

    @media (max-width: 600px) {
      .content {
        padding: 20px;
      }

      .info-row {
        flex-direction: column;
        align-items: flex-start;
      }

      .info-label {
        min-width: auto;
        margin-bottom: 4px;
      }
    }
  </style>
</head>
<body>
<div class="email-container">
  <div class="header">
    <h1>🚀 New Trial Registration</h1>
  </div>

  <div class="content">
    <div class="alert-badge">New User Alert</div>

    <p>A new user has registered for a trial account on your platform.</p>

    <div class="user-info">
      <div class="info-row">
        <span class="info-label">Name:</span>
        <span class="info-value">{{ user.full_name }}</span>
      </div>
      <div class="info-row">
        <span class="info-label">Email:</span>
        <span class="info-value">{{ user.email }}</span>
      </div>
      <div class="info-row">
        <span class="info-label">Site Name:</span>
        <span class="info-value">{{ site_name }}</span>
      </div>
      <div class="info-row">
        <span class="info-label">Phone:</span>
        <span class="info-value">{{ user.us_phone_number }}</span>
      </div>
        <div class="info-row">
            <span class="info-label">Location:</span>
            <span class="info-value">{{ location }}</span>
        </div>
      <div class="info-row">
        <span class="info-label">User ID:</span>
        <span class="info-value">#{{ user.id }}</span>
      </div>

    </div>


  </div>

  <div class="footer">
    <p>This is an automated notification from your admin panel.</p>
    <p class="timestamp">Sent on {{ current_time.strftime('%B %d, %Y at %I:%M %p UTC') }}</p>
  </div>
</div>
</body>
</html>