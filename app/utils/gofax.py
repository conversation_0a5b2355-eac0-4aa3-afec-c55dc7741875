import requests
import base64
from dataclasses import dataclass
from typing import <PERSON><PERSON>
from datetime import datetime
from config import Config


@dataclass
class SendFaxObject:
    send_to: str
    send_from: str
    filename: str
    file_bytes: Optional[bytes] = None
    file_base64: Optional[str] = None
    subject: Optional[str] = ""
    header: Optional[str] = ""
    csid: Optional[str] = ""
    retries: int = 3
    scheduled_time: Optional[datetime] = None
    client_reference: Optional[str] = None
    is_high_quality: bool = False


class GoFax:
    def __init__(self, api_key: Optional[str] = None, api_url: Optional[str] = None):
        self.api_key = api_key or Config.GOFAX_API_KEY
        self.api_url = api_url or Config.GOFAX_API_URL

    def send_fax(self, request: SendFaxObject) -> dict:
        """
        Sends a fax using the GoFax API with a base64-encoded document from the SendFaxObject.
        Returns a structured result dict indicating status and details.
        """

        # Determine document content
        if request.file_base64:
            base64_data = request.file_base64
        elif request.file_bytes:
            base64_data = base64.b64encode(request.file_bytes).decode("utf-8")
        else:
            raise ValueError("SendFaxObject must include either file_bytes or file_base64.")

        # Construct payload
        payload = {
            "SendTo": request.send_to,
            "SendFrom": request.send_from,
            "Subject": request.subject,
            "Header": request.header,
            "Csid": request.csid,
            "Retries": request.retries,
            "IsHighQuality": request.is_high_quality,
            "ClientReference": request.client_reference or f"ref_{datetime.utcnow().timestamp()}",
            "Documents": [
                {
                    "Filename": request.filename,
                    "Data": base64_data
                }
            ]
        }

        if request.scheduled_time:
            payload["ScheduledSendTime"] = request.scheduled_time.isoformat()

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        try:
            response = requests.post(
                f"{self.api_url}/Fax/SendFax",
                json=payload,
                headers=headers
            )
            response.raise_for_status()
        except requests.HTTPError as e:
            # Log the error body for debugging
            print(f"[GoFax] Error: {e.response.status_code} - {e.response.text}")
            raise

        result = response.json()
        documents = result.get("Documents", [])
        doc_info = documents[0] if documents else {}

        return {
            "success": result.get("Success", False),
            "message": result.get("Message", ""),
            "submitted": doc_info.get("Submitted", False),
            "filename": doc_info.get("Filename", ""),
            "document_message": doc_info.get("Message", ""),
            "validation_errors": result.get("ValidationErrors", [])
        }

    def get_fax_status(self, fax_id: int) -> dict:
        """
        Polls GoFax for the status of a specific fax job using the faxId.
        Returns a structured dictionary with job status information.
        """
        url = f"{self.api_url}/v1.0/SendFax/{fax_id}/Status"
        params = {"token": self.api_key}

        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
        except requests.HTTPError as e:
            print(f"[GoFax] Status Poll Error: {e.response.status_code} - {e.response.text}")
            raise

        data = response.json()

        return {
            "success": data.get("Success", False),
            "message": data.get("Message", ""),
            "response": data.get("Response", ""),
            "in_progress": data.get("InProgress", False),
            "error": data.get("Error", False),
            "client_reference": data.get("ClientReference"),
            "validation_errors": data.get("ValidationErrors", [])
        }
