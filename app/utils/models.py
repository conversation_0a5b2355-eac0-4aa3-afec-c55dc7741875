from flask import g
from flask import has_request_context, session
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import declared_attr

from app import db
from sqlalchemy_utils import Timestamp
from audit_mixin import AuditableMixin


class Site(db.Model, Timestamp):
    __tablename__ = 'site'
    id = db.Column(db.Integer, primary_key=True, nullable=False)
    name = db.Column(db.Unicode, nullable=False)
    enable_otp = db.Column(db.<PERSON>, default=False)
    users = db.relationship(
        "User", secondary='user_sites', back_populates='sites')
    labs = db.relationship("Lab", back_populates="site")

    def __str__(self):
        return self.name

    @hybrid_property
    def site_id(self):
        return self.id

    def copy_default_site_config(self, commit=True):
        """Copy data from default site (id=1) to the new site."""
        from app.dashboard.extra_models import PrefsFields, PrefsFieldItems, PrefsPredNew, ListLanguage, ListNationality
        from app.dashboard.models import SidebarModule

        # Copy PrefsFields and their items
        default_prefs_fields = PrefsFields.query.filter_by(site_id=1).all()
        for field in default_prefs_fields:
            # Create new PrefsField
            new_field = PrefsFields(
                fieldname=field.fieldname,
                default_fielditem_id=field.default_fielditem_id,
                site_id=self.id
            )
            db.session.add(new_field)
            db.session.flush()  # Flush to get the new field_id

            # Copy associated PrefsFieldItems
            for item in field.fielditems:
                new_item = PrefsFieldItems(
                    field_id=new_field.field_id,
                    fielditem=item.fielditem
                )
                db.session.add(new_item)

        # Copy SidebarModules
        default_modules = SidebarModule.query.filter_by(site_id=1).all()
        for module in default_modules:
            new_module = SidebarModule(
                title=module.title,
                url=module.url,
                icon=module.icon,
                enabled=module.enabled,
                order=module.order,
                site_id=self.id
            )
            db.session.add(new_module)

        # Copy SiteSettings
        default_settings = SiteSettings.query.filter_by(site_id=1).all()
        for setting in default_settings:
            new_setting = SiteSettings(
                name=setting.name,
                value=setting.value,
                value_json=setting.value_json,
                site_id=self.id
            )
            db.session.add(new_setting)

        # Copy PrefsPredNew
        default_pred_prefs = PrefsPredNew.query.filter_by(site_id=1).all()
        for pred_pref in default_pred_prefs:
            new_pred_pref = PrefsPredNew(
                equationid=pred_pref.equationid,
                age_clipmethod=pred_pref.age_clipmethod,
                age_clipmethodid=pred_pref.age_clipmethodid,
                ht_clipmethod=pred_pref.ht_clipmethod,
                ht_clipmethodid=pred_pref.ht_clipmethodid,
                wt_clipmethod=pred_pref.wt_clipmethod,
                wt_clipmethodid=pred_pref.wt_clipmethodid,
                active=pred_pref.active,
                startdate=pred_pref.startdate,
                enddate=pred_pref.enddate,
                markfordeletion=pred_pref.markfordeletion,
                lastedit=pred_pref.lastedit,
                lasteditby=pred_pref.lasteditby,
                site_id=self.id
            )
            db.session.add(new_pred_pref)

        # Copy ListLanguage
        default_languages = ListLanguage.query.filter_by(site_id=1).all()
        for language in default_languages:
            new_language = ListLanguage(
                hl7_code=language.hl7_code,
                hl7_description=language.hl7_description,
                code=language.code,
                description=language.description,
                enabled=language.enabled,
                site_id=self.id
            )
            db.session.add(new_language)

        # Copy ListNationality
        default_nationalities = ListNationality.query.filter_by(site_id=1).all()
        for nationality in default_nationalities:
            new_nationality = ListNationality(
                hl7_code=nationality.hl7_code,
                hl7_description=nationality.hl7_description,
                code=nationality.code,
                description=nationality.description,
                site_id=self.id
            )
            db.session.add(new_nationality)

        if commit:
            db.session.commit()

def get_site_id():
    if has_request_context() and session.get('site_id'):
        return session.get('site_id')
    else:
        return Site.query.first().id


class SiteMixin(object):
    @declared_attr
    def site_id(self):
        return db.Column('site_id', db.ForeignKey('site.id'), default=get_site_id)

    @declared_attr
    def site(self):
        return db.relationship("Site", foreign_keys=self.site_id)


class GeneralNameModelMixin(Timestamp):
    id = db.Column(db.Integer, primary_key=True)  # rezibase id
    name = db.Column(db.Unicode(), index=True, nullable=False)
    description = db.Column(db.String(200))

    @declared_attr
    def site_id(self):
        return db.Column('site_id', db.ForeignKey('site.id'), default=get_site_id)

    @declared_attr
    def site(self):
        return db.relationship("Site", foreign_keys=self.site_id)

    def __str__(self):
        return self.name


class SiteSettings(db.Model, GeneralNameModelMixin, AuditableMixin):
    __tablename__ = 'site_settings'
    __table_args__ = (db.UniqueConstraint('site_id', 'name'),)
    value = db.Column(db.Unicode())
    value_json = db.Column(db.JSON)

    @staticmethod
    def get(site_id, name, ignore_none=False):
        site_setting = SiteSettings.query.filter(
            SiteSettings.site_id == site_id, SiteSettings.name == name).first()
        if site_setting is None and ignore_none is False:
            raise Exception(f"Setting {name} Missing")
        return site_setting

    @staticmethod
    def get_json(site_id, name, ignore_none=False):
        site_setting = SiteSettings.query.filter(
            SiteSettings.site_id == site_id, SiteSettings.name == name).first()
        if site_setting is None and ignore_none is False:
            raise Exception(f"Setting {name} Missing")
        if site_setting:
            return site_setting.value_json
        return None

    @staticmethod
    def get_enabled(site_id, name):
        site_setting = SiteSettings.query.filter(
            SiteSettings.site_id == site_id, SiteSettings.name == name).first()
        return False if site_setting is None or site_setting.value is None else site_setting.value == 'Y'

    def __str__(self):
        return self.receiver


class AuditMixin(Timestamp):
    """
        AuditMixin
        Mixin for models, adds 4 columns to stamp,
        time and user on creation and modification
        will create the following columns:
        :created:
        :updated:
        :created by:
        :updated by:
    """
    @declared_attr
    def created_by_fk(cls):
        return db.Column(
            db.Integer, db.ForeignKey("ab_user.id"), default=cls.get_user_id, nullable=False
        )

    @declared_attr
    def created_by(cls):
        return db.relationship(
            "User",
            primaryjoin="%s.created_by_fk == User.id" % cls.__name__,
            enable_typechecks=False,
        )

    @declared_attr
    def updated_by_fk(cls):
        return db.Column(
            db.Integer,
            db.ForeignKey("ab_user.id"),
            default=cls.get_user_id,
            onupdate=cls.get_user_id,
            nullable=False,
        )

    @declared_attr
    def updated_by(cls):
        return db.relationship(
            "User",
            primaryjoin="%s.updated_by_fk == User.id" % cls.__name__,
            enable_typechecks=False,
        )

    @classmethod
    def get_user_id(cls):
        try:
            return g.user.id
        except Exception:
            return None
