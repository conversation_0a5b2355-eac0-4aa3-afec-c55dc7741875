from flask import Blueprint
from flask_login import current_user
from flask_admin.contrib.sqla import ModelView
from wtforms import <PERSON><PERSON>rea<PERSON>ield
from app import db
from app.utils.admin import SiteSpecificAdminMixin
from app.utils.models import SiteSettings, Site
from common import TrialModelView
from flask_wtf.file import FileField, FileAllowed
from wtforms import Form, validators, ValidationError
from werkzeug.datastructures import FileStorage
import base64


utils_bp = Blueprint(
    "utils_bp", __name__, template_folder="templates", static_folder="static"
)


class SiteAdmin(SiteSpecificAdminMixin, TrialModelView):
    site_id_field = 'id'
    set_site_id = False

    form_columns = ['name', 'enable_otp']

    def is_superuser(self):
        return current_user.is_authenticated and current_user.has_role('superuser')

    @property
    def can_delete(self):
        return self.is_superuser()

    @property
    def can_create(self):
        return self.is_superuser()

    def site_filter(self):
        if self.is_superuser:
            return None
        return self.model.site_id == self.get_current_site_id()

    def after_model_change(self, form, model: Site, is_created):
        """Override to copy default site data when a new site is created."""
        res = super(ModelView, self).after_model_change(form, model, is_created)
        if is_created:
            model.copy_default_site_config()
        return res


class SiteSettingAdmin(SiteSpecificAdminMixin, TrialModelView):
    form_excluded_columns = ['site', 'created', 'updated']
    site_id_field = 'site_id'
    column_list = ['id', 'name', 'value', 'value_json', 'created']

    def _short_json_formatter(view, context, model, name):
        val = getattr(model, name)
        if not val:
            return ''
        return str(val)[:100] + '...' if len(str(val)) > 100 else str(val)

    column_formatters = {
        'value_json': _short_json_formatter,
    }

    # 👇 Here we override the form field for `value` to be a multi-line input
    form_overrides = {
        'value': TextAreaField
    }

    form_extra_fields = {
        'image_upload': FileField(
            'Upload Image',
            validators=[FileAllowed(['jpg', 'jpeg', 'png'], 'Images only!')]
        )
    }

    def is_action_allowed(self, name):
        return super().is_action_allowed(name)

    def on_model_change(self, form, model, is_created):
        image_file: FileStorage = form.image_upload.data
        if image_file:
            image_bytes = image_file.read()
            encoded_string = base64.b64encode(image_bytes).decode('utf-8')
            image_mime = image_file.mimetype

            model.value_json = {
                "image": {
                    "mimetype": image_mime,
                    "data": encoded_string
                }
            }

        super().on_model_change(form, model, is_created)


def add_admins(admin):
    admin.add_view(SiteSettingAdmin(SiteSettings, db.session, category="Misc. Settings"))
    admin.add_view(SiteAdmin(Site, db.session, category='Misc. Settings'))
