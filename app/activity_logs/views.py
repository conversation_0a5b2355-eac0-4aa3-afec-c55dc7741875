from flask import Blueprint, request, jsonify
from .models import ActivityLog
from datetime import datetime

activity_logs_bp = Blueprint('activity_logs_bp', __name__)

@activity_logs_bp.route('/webhook/activity', methods=['POST'])
def activity_webhook():
    if not request.is_json:
        return jsonify({'success': False, 'error': 'Invalid or missing JSON payload'}), 400

    payload = request.json

    event = payload.get('event', {})
    table_info = payload.get('table', {})
    data = event.get('data', {})
    delivery_info = payload.get('delivery_info', {})
    trigger_info = payload.get('trigger', {})

    operation = event.get('op')
    table_name = table_info.get('name')

    if not operation:
        return jsonify({'success': False, 'error': 'Missing operation field in event payload'}), 400
    if not table_name:
        return jsonify({'success': False, 'error': 'Missing table name field in payload'}), 400


    session_vars = event.get('session_variables') or {}
    user_id = None
    hasura_user_id_str = session_vars.get('x-hasura-user-id')
    if hasura_user_id_str:
        try:
            user_id = int(hasura_user_id_str)
        except (ValueError, TypeError):
            user_id = None

    record_id = None
    if operation in ['INSERT', 'UPDATE']:
        new_data = data.get('new', {})
        record_id = new_data.get('id')
    elif operation == 'DELETE':
        old_data = data.get('old', {})
        record_id = old_data.get('id')

    ip_address = request.headers.get('X-Forwarded-For', request.remote_addr or 'UNKNOWN_IP')
    user_agent = request.headers.get('User-Agent', 'UNKNOWN_USER_AGENT')
    content_type = request.headers.get('Content-Type', 'UNKNOWN_CONTENT_TYPE')

    request_headers = dict(request.headers)



    details = {
        'hasura_payload': payload,
        'event': {
            'operation': operation,
            'table': {
                'name': table_name,
                'schema': table_info.get('schema', 'public') # Default schema
            },
            'trigger_name': trigger_info.get('name', 'unknown_trigger'), # Default trigger name
            'hasura_event_id': payload.get('id', 'no_event_id'), # Default event ID
            'timestamp': payload.get('created_at', datetime.utcnow().isoformat()) # Default timestamp
        },
        'data_changes': data,
        'user_context': session_vars,
        'request_metadata': {
            'ip_address': ip_address,
            'user_agent': user_agent,
            'content_type': content_type,
            'headers': request_headers,
            'processed_at': datetime.utcnow().isoformat(),
            'webhook_version': '1.1' # Increment version if significant changes
        },
        'delivery_info': delivery_info, # Already defaulted to {}
        'trace_context': event.get('trace_context', {}) # Default trace context
    }

    if operation == 'UPDATE':
        old_data = data.get('old', {})
        new_data = data.get('new', {})
        changed_fields = {}
        for key, new_value in new_data.items():
            old_value = old_data.get(key)
            if new_value != old_value:
                changed_fields[key] = {
                    'old': old_value,
                    'new': new_value
                }
        details['change_summary'] = {
            'fields_changed': list(changed_fields.keys()),
            'changes_count': len(changed_fields),
            'changed_fields': changed_fields
        }
    else:
        details['change_summary'] = {}


    activity_log = ActivityLog.create_log(
        user_id=user_id,
        action_type=operation,
        table_name=table_name,
        record_id=record_id,
        details=details,
        ip_address=ip_address
    )

    return jsonify({
        'success': True,
        'message': 'Activity logged successfully',
        'log_id': activity_log.id if activity_log else None, # Safely access log_id
        'captured': {
            'operation': operation,
            'table': table_name,
            'record_id': record_id,
            'user_id': user_id,
            'data_size': len(str(details)) # Approximate size for logging
        }
    }), 200

@activity_logs_bp.route('/webhook/health', methods=['GET'])
def webhook_health():
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat()
    }), 200