{"name": "fe", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build --emptyOutDir", "typecheck": "tsc -b", "lint": "eslint .", "preview": "vite preview", "prettier": "npx prettier --write .", "graphql-codegen": "node graphql-tada.js", "postinstall": "npm run graphql-codegen"}, "dependencies": {"@apollo/client": "^3.13.1", "@gql.tada/cli-utils": "^1.6.3", "@internationalized/date": "^3.7.0", "@mantine/hooks": "^7.16.0", "@preact/signals-react": "^3.0.1", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.5", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.6", "@react-aria/focus": "^3.19.1", "@react-aria/utils": "^3.26.0", "@react-pdf/renderer": "^4.3.0", "@react-stately/utils": "^3.10.5", "@react-types/shared": "^3.26.0", "@smastrom/react-rating": "^1.5.0", "@svgr/plugin-svgo": "^8.1.0", "@tanstack/query-sync-storage-persister": "^5.64.1", "@tanstack/react-pacer": "^0.1.0", "@tanstack/react-query": "^5.64.1", "@tanstack/react-query-persist-client": "^5.64.1", "@tanstack/react-table": "^8.20.6", "@tiptap/core": "^2.11.5", "@tiptap/extension-document": "^2.11.5", "@tiptap/extension-mention": "^2.11.5", "@tiptap/extension-paragraph": "^2.11.5", "@tiptap/extension-placeholder": "^2.11.7", "@tiptap/extension-text": "^2.11.5", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@tiptap/suggestion": "^2.11.5", "@zag-js/editable": "^0.82.1", "@zag-js/react": "^0.82.1", "ag-grid-enterprise": "^33.2.4", "ag-grid-react": "^33.2.4", "apollo3-cache-persist": "^0.15.0", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "d3-scale": "^4.0.2", "date-fns": "^4.1.0", "fuse.js": "^7.1.0", "gql.tada": "^1.8.10", "graphql-ws": "^6.0.4", "html-to-image": "^1.11.13", "idb-keyval": "^6.2.1", "jotai": "^2.11.0", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "jwt-decode": "^4.0.0", "lucide-react": "^0.471.1", "mathjs": "^14.4.0", "mobx": "^6.13.6", "mobx-react-lite": "^4.1.0", "motion": "^11.18.0", "non.geist": "^1.0.4", "nuqs": "^2.4.0", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-aria": "^3.36.0", "react-aria-components": "^1.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-pdf-tailwind": "^2.3.0", "react-resizable-panels": "^2.1.9", "react-router": "^7.1.1", "react-stately": "^3.34.0", "recharts": "^2.15.0", "stable-hash": "^0.0.5", "tailwind-merge": "^2.6.0", "tailwind-variants": "^0.3.0", "tailwindcss-animate": "^1.0.7", "ts-toolbelt": "^9.6.0", "tw-merge": "^0.0.1-alpha.3", "vaul": "^1.1.2", "vite-plugin-svgr": "^4.3.0"}, "devDependencies": {"@eslint/js": "^9.17.0", "@graphql-codegen/cli": "^5.0.5", "@graphql-codegen/client-preset": "^4.6.3", "@graphql-typed-document-node/core": "^3.2.0", "@tailwindcss/postcss": "^4.0.0", "@tailwindcss/vite": "^4.0.0", "@total-typescript/ts-reset": "^0.6.1", "@trivago/prettier-plugin-sort-imports": "^5.2.1", "@types/d3-scale": "^4.0.9", "@types/node": "^22.10.7", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "dotenv-flow": "^4.1.0", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "graphql": "^16.10.0", "postcss": "^8.4.49", "prettier": "3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.0.0", "typescript": "~5.8.2", "typescript-eslint": "^8.32.1", "vite": "^6.0.5", "vite-plugin-top-level-await": "^1.5.0", "vitest": "^3.2.3"}}