import {useEffect, useMemo, useState} from 'react';
import {
  <PERSON>alog,
  <PERSON>ing,
  ListBox,
  ListBoxItem,
  Modal,
  Popover,
  Button as RACButton,
  SelectValue,
} from 'react-aria-components';
import {useParams} from 'react-router';

import {useQuery} from '@apollo/client';
import {ChevronDown, X} from 'lucide-react';

import NIL from '@/components/NIL';
import {Input, Label} from '@/components/ui/Field';
import {But<PERSON>} from '@/components/ui/button';
import {Form, FormRootErrors, NumberFormField, SelectFormField, TextFormField} from '@/components/ui/form';
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from '@/components/ui/table.tsx';
import {EquationLoadMethod} from '@/graphql/equations';
import {getTestDetail} from '@/graphql/normal-values.ts';
import {getEthnicities, getRFTGenders} from '@/graphql/patients';
import {Test} from '@/store/test.store.ts';

interface Props {
  sourceName?: string;
  sourceId: number;
  testName: string;
  testId: number;
  isOpen: boolean;
  onClose: () => void;
}

export function EquationCalculator({testName, sourceName, sourceId, isOpen, onClose}: Props) {
  const {testId} = useParams<{testId: string}>();
  const [calculationResults, setCalculationResults] = useState<null | Awaited<ReturnType<Test['calcPred']>>>(
    null
  );

  const {data: testData} = useQuery(getTestDetail, {
    variables: {testid: +testId!},
  });

  const test = useMemo(() => new Test(testData?.pred_ref_tests[0] as any), [testData]);

  const {data: gendersData} = useQuery(getRFTGenders);
  const {data: ethnicitiesData} = useQuery(getEthnicities);

  // Define initial values for the form
  const initialValues = {
    ...{
      height: 170,
      weight: 70,
      genderId: 1,
      age: 55.5,
      ethnicityId: 1,
    },
    test: testName || '',
    source: sourceName || '',
  };

  useEffect(() => {
    setCalculationResults(null);
  }, [isOpen]);

  const handleCalculate = async (values: any) => {
    setCalculationResults(
      await test.calcPred({
        sourceid: sourceId,
        testid: +testId!,
        testdate: new Date(),
        age: values.age,
        Htcm: values.height,
        Wtkg: values.weight,
        gender_for_rfts_id: values.genderId,
        gender_for_rfts: gendersData?.pred_ref_genders.find((g) => g.id === values.genderId)?.gender!,
        ethnicity_id: values.ethnicityId,
        ethnicity: ethnicitiesData?.pred_ref_ethnicities.find((g) => g.id === values.ethnicityId)
          ?.description!,
        load_method: EquationLoadMethod.UseSourcesSpecified,
      })
    );
  };

  return (
    <Modal
      isDismissable
      isOpen={isOpen}
      onOpenChange={(open) => !open && onClose()}
      className="react-aria-Drawer"
    >
      <Dialog>
        <Heading slot="title">Calculation Checker</Heading>
        <RACButton
          slot="close"
          onPress={onClose}
        >
          <X />
        </RACButton>

        <Form
          values={initialValues}
          className="mt-6 flex-grow"
          id="calculator-form"
          onSubmit={handleCalculate}
        >
          <div className="space-y-3 pb-6">
            <TextFormField
              isReadOnly
              name="source"
            >
              <Label>Source</Label>
              <Input size="md" />
            </TextFormField>

            <div className="grid grid-cols-2 items-center gap-x-3">
              <NumberFormField
                name="height"
                required
                formatOptions={{
                  style: 'unit',
                  unit: 'centimeter',
                  unitDisplay: 'short',
                }}
              >
                <Label>Height (cm)</Label>
                <Input size="md" />
              </NumberFormField>

              <NumberFormField
                name="weight"
                required
                formatOptions={{
                  style: 'unit',
                  unit: 'kilogram',
                  unitDisplay: 'short',
                }}
              >
                <Label>Weight (kg)</Label>
                <Input size="md" />
              </NumberFormField>
            </div>

            <div className="grid grid-cols-2 items-center gap-x-3">
              <SelectFormField
                required
                name="genderId"
              >
                <Label>Gender</Label>
                <RACButton className="react-aria-Button w-full">
                  <SelectValue className="react-aria-SelectValue text-sm/[1.3]" />
                  <ChevronDown
                    className="size-4 text-gray-400"
                    aria-hidden="true"
                  />
                </RACButton>
                <Popover>
                  <ListBox items={gendersData?.pred_ref_genders ?? []}>
                    {(item) => <ListBoxItem id={item.id}>{item.gender}</ListBoxItem>}
                  </ListBox>
                </Popover>
              </SelectFormField>

              <NumberFormField
                required
                name="age"
              >
                <Label>Age</Label>
                <Input size="md" />
              </NumberFormField>
            </div>

            <SelectFormField
              required
              name="ethnicityId"
            >
              <Label>Ethnicity</Label>
              <RACButton className="react-aria-Button w-full">
                <SelectValue className="react-aria-SelectValue text-sm/[1.3]" />
                <ChevronDown
                  className="size-4 text-gray-400"
                  aria-hidden="true"
                />
              </RACButton>
              <Popover>
                <ListBox items={ethnicitiesData?.pred_ref_ethnicities ?? []}>
                  {(item) => <ListBoxItem id={item.id}>{item.description}</ListBoxItem>}
                </ListBox>
              </Popover>
            </SelectFormField>

            <FormRootErrors />

            <div className="mt-4 flex justify-center">
              <Button
                type="submit"
                className="px-6"
              >
                Calculate
              </Button>
            </div>
          </div>
        </Form>

        {calculationResults && (
          <div className="mt-6 mb-10">
            <h3 className="mb-2 text-lg font-medium">Results</h3>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="top-0">Parameter</TableHead>
                    <TableHead className="top-0 text-right">MPV</TableHead>
                    <TableHead className="top-0 text-right">LLN</TableHead>
                    <TableHead className="top-0 text-right">ULN</TableHead>
                    <TableHead className="top-0 text-right">Range</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {calculationResults.map(({param, pred}, index: number) => (
                    <TableRow key={index}>
                      <TableCell>
                        {param.description} [{param.units}]
                      </TableCell>
                      <TableCell className="text-right">
                        {pred?.mpv ? param.valueFormater.format(pred?.mpv) : <NIL />}
                      </TableCell>
                      <TableCell className="text-right">
                        {pred?.lln ? param.valueFormater.format(pred.lln) : <NIL />}
                      </TableCell>
                      <TableCell className="text-right">
                        {pred?.uln ? param.valueFormater.format(pred.uln) : <NIL />}
                      </TableCell>
                      <TableCell className="text-right">
                        {pred?.range ? (
                          <>
                            {param.valueFormater.format(pred?.range[0])} -
                            {param.valueFormater.format(pred?.range[1])}
                          </>
                        ) : (
                          <NIL />
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        )}
      </Dialog>
    </Modal>
  );
}
