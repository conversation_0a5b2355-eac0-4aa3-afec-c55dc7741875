import {Input} from 'react-aria-components';
import {useNavigate} from 'react-router';

import {Search as SearchIcon} from 'lucide-react';
import {parseAsString, useQueryState} from 'nuqs';

import {useDialogState} from '@/components/modal-state-provider.tsx';
import {Button} from '@/components/ui/button';
import AddPatient from '@/views/patients/components/add-patient.tsx';

export function PatientsViewInner() {
  const navigate = useNavigate();
  const [searchValue, setSearchValue] = useQueryState('search', parseAsString.withDefault(''));
  const [, setAddPatient] = useDialogState('add-patient');

  const handleSearch = () =>
    navigate({
      pathname: 'all',
      search: `?search=${encodeURIComponent(searchValue.trim())}`,
    });

  return (
    <div className="h-full w-full">
      <div className="flex h-full flex-col items-center justify-center">
        <div className="flex flex-col gap-10">
          <div className="flex flex-col items-center gap-3">
            <div className="text-4xl font-bold text-neutral-900">Find Patients</div>
            <div className="text-neutral-700">Search patients using their Name or MRN</div>
          </div>

          <div className="flex flex-col gap-8">
            <div className="flex h-16 w-170 rounded-md border border-neutral-400 p-2 shadow-sm">
              <div className="relative w-full">
                <SearchIcon className="pointer-events-none absolute top-2.75 left-3 h-6 w-6 shrink-0 text-neutral-600" />

                <Input
                  className="h-full w-full px-3 pl-14 outline-none"
                  aria-label="Search patients by name or MRN"
                  placeholder="Enter patient name or MRN"
                  value={searchValue}
                  onChange={(e) => setSearchValue(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && searchValue.trim()) {
                      handleSearch();
                    }
                  }}
                />
              </div>
              <Button
                variant="solid"
                className="flex h-12 items-center justify-center"
                onPress={() => searchValue.trim() && handleSearch()}
              >
                Search
              </Button>
            </div>

            <div className="flex justify-center gap-3">
              <Button
                onPress={() => setAddPatient(true)}
                variant="outlined"
                className="react-aria-Button text-md data-hovered:text-brand-500 data-focused:text-brand-500 text-brand-500 h-12 w-44.5 rounded-sm border border-neutral-300 bg-transparent p-1.5 px-3 text-xs font-semibold focus:outline-none"
              >
                Add new patient
              </Button>
              <Button
                variant="outlined"
                className="react-aria-Button text-md data-hovered:text-brand-500 data-focused:text-brand-500 text-brand-500 h-12 w-44.5 rounded-sm border border-neutral-300 bg-transparent p-1.5 px-3 text-xs font-semibold focus:outline-none"
                onPress={() => navigate({pathname: 'all'})}
              >
                View all
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function PatientsSearch() {
  return (
    <>
      <PatientsViewInner />
      <AddPatient />
    </>
  );
}
