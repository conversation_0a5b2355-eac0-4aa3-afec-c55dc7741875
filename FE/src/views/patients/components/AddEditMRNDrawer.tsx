import {ComponentProps, useState} from 'react';
import {
  Dialog,
  Heading,
  Key,
  ListBox,
  ListBoxItem,
  Modal,
  Popover,
  Button as RACButton,
  Select,
  SelectValue,
} from 'react-aria-components';
import {useParams} from 'react-router';

import {useApolloClient, useQuery} from '@apollo/client';
import {Check, ChevronDown, X} from 'lucide-react';

import {useDialogState} from '@/components/modal-state-provider.tsx';
import {Input, Label} from '@/components/ui/Field.tsx';
import {Button} from '@/components/ui/button.tsx';
import {Form, FormField, FormRootErrors, TextFormField} from '@/components/ui/form';
import {FieldError} from '@/components/ui/form/FieldError.tsx';
import {getHealthServicesList, getPatientsDetailData, getPrimaryUr, getSiteConfigHealthServicesList} from '@/graphql/patients.ts';
import {
  logMerge,
  mutateAddPatientMRN,
  updateMergeInfo,
  updateOtherURs,
  updatePrimaryUR,
} from '@/views/patients/patient-detail/patient-detail-sidebar/mutation.ts';

function AddEditMrnDrawer(
  props: ComponentProps<typeof Modal> & {
    type: 'add' | 'edit';
    defaultValue?: string | null;
    healthService?: number | null;
  }
) {
  const {type, defaultValue, healthService} = props;
  const {patientId} = useParams();
  const [isOpen, setIsOpen] = useDialogState('add-edit-MRN-dialogue');
  const [duplicateError, setDuplicateError] = useState<string | null>(null);

  const {data: HealthServicesList} = useQuery(getHealthServicesList);
  const {data: SiteConfigHealthServices} = useQuery(getSiteConfigHealthServicesList);
  const healthServicesList = HealthServicesList?.list_healthservices?.filter((hs) => hs?.code !== null);
  const siteConfigHealthServices = SiteConfigHealthServices?.site_config_healthservices
    ?.filter((schs) => schs.enabled)
    .map((schs) => schs.hsid);

  const healthServices = healthServicesList?.filter((hs) => siteConfigHealthServices?.includes(hs?.code!));

  const client = useApolloClient();

  const addPatientMRN = async (
    patientId: string | undefined,
    ur: number | string,
    ur_hsid: number | string,
    status = 'other_healthservice',
    mergeToUrId = null
  ) => {
    try {
      if (!['primary', 'secondary', 'other_healthservice'].includes(status)) {
        throw new Error("Invalid status. Must be 'primary', 'secondary', or 'other_healthservice'");
      }

      if (status === 'secondary' && !mergeToUrId) {
        throw new Error("When setting status to 'secondary', mergeToUrId must be provided");
      }

      if (!patientId || isNaN(Number(patientId))) {
        throw new Error('Invalid patient ID');
      }

      const { data: existingData } = await client.query({
        query: getPatientsDetailData,
        variables: { patientId: Number(patientId) },
        fetchPolicy: 'network-only',
      });

      const existingMRNs = existingData?.pas_pt?.[0]?.pas_pt_ur_numbers || [];

      const duplicate = existingMRNs.some((mrn: any) => {
        return String(mrn.ur) === String(ur) && String(mrn.ur_hsid) === String(ur_hsid);
      });

      if (duplicate) {
        setDuplicateError('This MRN and Health Service combination already exists for this patient.');
        return;
      }

      const { data } = await client.mutate({
        mutation: mutateAddPatientMRN,
        variables: {
          patientId,
          ur,
          ur_hsid,
          ur_status: status,
        },
      });

      const newUrRecord = data.insert_pas_pt_ur_numbers_one;

      switch (status) {
        case 'primary':
          await client.mutate({
            mutation: updatePrimaryUR,
            variables: {
              patientId,
              ur_id: newUrRecord.ur_id,
              ur: newUrRecord.ur,
              ur_hsid: newUrRecord.ur_hsid,
            },
          });

          await client.mutate({
            mutation: updateOtherURs,
            variables: {
              patientId,
              ur_id: newUrRecord.ur_id,
            },
          });
          break;

        case 'secondary':
          const { data: primaryData } = await client.query({
            query: getPrimaryUr,
            variables: {
              ur_id: mergeToUrId ?? 0,
            },
          });

          const primaryUr = primaryData.pas_pt_ur_numbers[0];

          await client.mutate({
            mutation: updateMergeInfo,
            variables: {
              patientId,
              ur_mergeto: primaryUr.ur,
              ur_mergeto_urid: mergeToUrId,
              ur_mergeto_hsid: primaryUr.ur_hsid,
            },
          });

          await client.mutate({
            mutation: logMerge,
            variables: {
              parentPatientId: primaryUr.patientid,
              childPatientId: patientId,
            },
          });
          break;

        case 'other_healthservice':
          break;
      }

      await client.refetchQueries({
        include: [getPatientsDetailData],
      });

      setDuplicateError(null);
      setIsOpen(false);
      return newUrRecord;
    } catch (error) {
      console.error('Error adding MRN:', error);
      throw error;
    }
  };

  return (
    <Modal
      isDismissable
      isOpen={isOpen}
      onOpenChange={setIsOpen}
      className="react-aria-Modal w-95 max-w-xl overflow-y-auto rounded-md p-0"
      {...props}
    >
      <Dialog>
        <Form
          onSubmit={(data) => {
            addPatientMRN(patientId, data.ur.toString(), data.hsid.toString());
          }}
        >
          <FormRootErrors />

          <div className="flex items-center justify-between border-b border-neutral-200 px-6 py-4 text-neutral-800">
            <Heading
              className="text-sm font-semibold"
              slot="title"
            >
              {type === 'add' ? 'Add' : 'Edit'} MRN
            </Heading>
            <div onClick={() => setIsOpen(false)}>
              <X className="h-4 w-4" />
            </div>
          </div>

          <div className="px-6 py-8">
            <TextFormField
              aria-label="MRN"
              isRequired
              defaultValue={defaultValue}
              name="ur"
              className="mb-4 w-full"
            >
              <Label className="mb-2 text-xs text-neutral-700">MRN</Label>
              <Input
                placeholder="Enter MRN"
                className="react-aria-Input bg-neutral-100 text-neutral-800"
                onChange={() => setDuplicateError(null)}
              />
              <FieldError />
            </TextFormField>

            <FormField
              name="hsid"
              defaultValue={healthService}
            >
              <Select
                className="react-aria-Select"
                placeholder="Select Health Service"
                onSelectionChange={() => setDuplicateError(null)}
              >
                <Label className="mb-2 text-xs text-neutral-700">Health Service</Label>
                <RACButton className="react-aria-Button w-full bg-neutral-100">
                  <SelectValue className="react-aria-SelectValue text-sm" />
                  <ChevronDown />
                </RACButton>
                <Popover>
                  <ListBox items={healthServices}>
                    {(hs) => (
                      <ListBoxItem
                        id={hs?.code as Key}
                        textValue={hs?.description ?? ''}
                      >
                        <div className="flex w-full cursor-pointer items-center justify-between">
                          <span>{hs?.description}</span>
                          <Check className="text-white-white hidden h-4 w-4 [[data-selected=true]_&]:block" />
                        </div>
                      </ListBoxItem>
                    )}
                  </ListBox>
                </Popover>
              </Select>
            </FormField>

            {duplicateError && (
              <div className="rounded bg-red-50 px-3 py-2 text-sm text-red-600 border border-red-300 mt-4">
                {duplicateError}
              </div>
            )}
          </div>

          <div className="flex items-center gap-x-3 px-6 py-4">
            <Button
              onPress={() => setIsOpen(false)}
              className="text-brand-500 hover:!text-brand-500 w-full rounded-sm border-neutral-400 font-bold"
              variant="outlined"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="w-full rounded-sm border-neutral-400 font-bold"
            >
              {type === 'add' ? 'Add' : 'Update'}
            </Button>
          </div>
        </Form>
      </Dialog>
    </Modal>
  );
}

export default AddEditMrnDrawer;
