import {useMemo} from 'react';

import {useQuery} from '@apollo/client';

import {RftSession as TestSession} from '@/features/studies-rft/store/rft-session.store.ts';
import {RftStore} from '@/features/studies-rft/store/rtf.store.ts';
import {getPatientRespiratoryTrend} from '@/graphql/patients.ts';
import {useGlobalStoreLoader} from '@/store/global.store.ts';

export type Test = {
  testdate: string;
  testtime: string;
  testtype: string;
  r_bl_fer: string;
  weight: string;
  technicalnotes: string;
  report_text: string;
};

function UseComputeRftTrendData({patientId}: {patientId: string}) {
  const {data: RespiratoryTrend, loading: isRespiratoryTrendLoading} = useQuery(getPatientRespiratoryTrend, {
    variables: {patientId: parseInt(patientId ?? '')},
  });

  useGlobalStoreLoader();

  const {r_sessions, rft_routine, prov_test} = RespiratoryTrend ?? {};

  const respiratoryLabTrendData = useMemo(() => {
    return (
      r_sessions?.flatMap((rSession) =>
        [
          ...(rft_routine?.filter((rft) => rft.sessionid === rSession.sessionid) || []),
          ...(prov_test?.filter((prov) => prov.sessionid === rSession.sessionid) || []),
        ].map((test) => ({...rSession, ...test}))
      ) || []
    );
  }, [RespiratoryTrend]);

  const stores = useMemo(() => {
    return respiratoryLabTrendData.map((sessionAndTest) => {
      const rftStore = new RftStore({...sessionAndTest, autoUpsert: false, autoFetch: false} as any);
      const testSession = new TestSession(sessionAndTest as any);

      // Manually initialize stores with data
      rftStore.fromQueryResult(sessionAndTest as any);

      return {
        rftStore,
        coTransferStore: rftStore.coTransferStore,
        spirometryStore: rftStore.spirometryStore,
        testSession: testSession,
        lungVolumeStore: rftStore.lungVolumesStore,
        mrpsStore: rftStore.mrpsStore,
        exhaledNitricOxideStore: rftStore.exhaledNitricOxideStore,
        test: sessionAndTest as Test,
      };
    });
  }, [respiratoryLabTrendData]);

  const sortedStores = useMemo(() => {
    return [...stores].sort((a, b) => {
      const dateTimeA = a.test.testtime
        ? new Date(`${a.test.testdate}T${a.test.testtime}`)
        : new Date(`${a.test.testdate}T00:00:00`);

      const dateTimeB = b.test.testtime
        ? new Date(`${b.test.testdate}T${b.test.testtime}`)
        : new Date(`${b.test.testdate}T00:00:00`);

      return dateTimeB.getTime() - dateTimeA.getTime();
    });
  }, [stores]);

  // Effect to ensure all stores are properly initialized with data
  // useEffect(() => {
  //   if (respiratoryLabTrendData.length > 0) {
  //     // Force update of all stores with their respective data
  //     stores.forEach((store, index) => {
  //       const sessionAndTest = respiratoryLabTrendData[index];
  //       if (sessionAndTest) {
  //         // Manually update each store with the data
  //         store.rftStore.fromQueryResult(sessionAndTest as any, true);
  //       }
  //     });
  //   }
  // }, [respiratoryLabTrendData, stores]);

  const rftHeaders = [
    // General Information
    {
      id: 'Weight',
      unit: 'kg',
      name: 'Weight',
      render: (item: (typeof stores)[number]) => item?.test?.weight,
    },
    {
      id: 'BMI',
      unit: 'kg/m2',
      name: 'BMI',
      render: (item: (typeof stores)[number]) => item?.testSession?.BMI?.toFixed(2),
    },
    // Spirometry
    {
      id: 'Test Type 1',
      name: 'Spirometry',
      testType: 'Spirometry',
    },
    {
      id: 'r_pre_condition',
      name: 'Pre-condition',
      testType: 'Spirometry',
      render: (item: (typeof stores)[number]) =>
        ((item?.test as any)?.r_pre_condition ?? (item?.test as any)?.bdstatus === 'Nil today')
          ? 'Baseline'
          : (item?.test as any)?.bdstatus,
    },
    {
      id: 'Pre FEV1',
      unit: 'L',
      name: 'Pre FEV1',
      testType: 'Spirometry',
      render: (item: (typeof stores)[number]) => item?.spirometryStore?.getSp1Value('FEV1')?.result
    },
    {
      id: 'Pre FVC',
      name: 'Pre FVC',
      unit: 'L',
      testType: 'Spirometry',
      render: (item: (typeof stores)[number]) => item?.spirometryStore?.getSp1Value('FVC')?.result,
    },
    {
      id: 'Pre VC',
      name: 'Pre VC',
      unit: 'L',
      testType: 'Spirometry',
      render: (item: (typeof stores)[number]) => item?.spirometryStore?.getSp1Value('VC')?.result,
    },
    {
      id: 'FEV1/FVC (%)',
      name: 'FEV1/FVC (%)',
      testType: 'Spirometry',
      render: (item: (typeof stores)[number]) =>
        item?.spirometryStore?.getSp1Value('FEV1/FVC')?.result?.toFixed(2),
    },
    {
      id: 'FEV1/VC (%)',
      name: 'FEV1/VC (%)',
      testType: 'Spirometry',
      render: (item: (typeof stores)[number]) =>
        item?.spirometryStore?.getSp1Value('FEV1/VC')?.result?.toFixed(2),
    },
    {
      id: 'FEF25-75 (L/sec)',
      name: 'FEF25-75',
      unit: 'L/sec',
      testType: 'Spirometry',
      render: (item: (typeof stores)[number]) => item?.spirometryStore?.getSp1Value('FEF25-75')?.result,
    },
    {
      id: 'PEF',
      name: 'PEF',
      unit: 'L/sec',
      testType: 'Spirometry',
      render: (item: (typeof stores)[number]) => item?.spirometryStore?.getSp1Value('PEF')?.result?.toFixed(2),
    },
    {
      id: 'Pre FER (%)',
      name: 'Pre FER (%)',
      testType: 'Spirometry',
      render: (item: (typeof stores)[number]) => item?.test?.r_bl_fer,
    },
    {
      id: 'r_post_condition',
      name: 'Post Condition',
      testType: 'Spirometry',
      render: (item: (typeof stores)[number]) => item?.spirometryStore?.sp2Condition,
    },
    {
      id: 'Post FEV1',
      name: 'Post FEV1',
      unit: 'L',
      testType: 'Spirometry',
      render: (item: (typeof stores)[number]) => item?.spirometryStore?.getSp2Value('FEV1')?.result,
    },
    {
      id: 'Post FVC',
      name: 'Post FVC',
      unit: 'L',
      testType: 'Spirometry',
      render: (item: (typeof stores)[number]) => item?.spirometryStore?.getSp2Value('FVC')?.result,
    },
    {
      id: 'Post VC',
      name: 'Post VC',
      unit: 'L',
      testType: 'Spirometry',
      render: (item: (typeof stores)[number]) => item?.spirometryStore?.getSp2Value('VC')?.result,
    },
    {
      id: 'Post FER (%)',
      name: 'Post FER (%)',
      testType: 'Spirometry',
      render: (item: (typeof stores)[number]) => (item?.test as any)?.r_post_fer,
    },

    // CO Transfer
    {
      id: 'Test Type 2',
      name: 'CO Transfer',
      testType: 'CO Transfer',
    },
    {
      id: 'VA',
      name: 'VA',
      unit: 'L',
      testType: 'CO Transfer',
      render: (item: (typeof stores)[number]) => item?.coTransferStore?.getParamValue('VA')?.result,
    },
    {
      id: 'TLCO',
      name: 'TLCO',
      unit: 'ml/min/mmHg',
      testType: 'CO Transfer',
      render: (item: (typeof stores)[number]) => item?.coTransferStore?.getParamValue('TLCO')?.result,
    },
    {
      id: 'KCO',
      name: 'KCO',
      unit: 'ml/min/mmHg',
      testType: 'CO Transfer',
      render: (item: (typeof stores)[number]) => item?.coTransferStore?.getParamValue('KCO')?.result,
    },
    {
      id: 'TLCO Hb',
      name: 'TLCO Hb',
      unit: 'ml/min/mmHg',
      testType: 'CO Transfer',
      render: (item: (typeof stores)[number]) =>
        item?.coTransferStore?.getParamValue('TLCO(Hb)')?.result?.toFixed(2),
    },
    {
      id: 'KCO Hb',
      name: 'KCO Hb',
      unit: 'ml/min/mmHg/L',
      testType: 'CO Transfer',
      render: (item: (typeof stores)[number]) =>
        item?.coTransferStore?.getParamValue('KCO(Hb)')?.result?.toFixed(2),
    },
    {
      id: 'Vi',
      name: 'Vi',
      unit: 'L',
      testType: 'CO Transfer',
      render: (item: (typeof stores)[number]) =>
        item?.coTransferStore?.getParamValue('Vi')?.result?.toFixed(2),
    },
    {
      id: 'Hb',
      name: 'Hb',
      unit: 'gm/dl',
      testType: 'CO Transfer',
      render: (item: (typeof stores)[number]) =>
        item?.coTransferStore?.getParamValue('Hb')?.result?.toFixed(2),
    },
    {
      id: 'Vi/VC (%)',
      name: 'Vi/VC (%)',
      testType: 'CO Transfer',
      render: (item: (typeof stores)[number]) =>
        item?.coTransferStore?.getParamValue('Vi/VC')?.result?.toFixed(2),
    },

    // Lung Volumes
    {
      id: 'Test Type 3',
      name: 'Lung Volumes',
      testType: 'Lung Volumes',
    },
    {
      id: 'TLC',
      name: 'TLC',
      unit: 'L',
      testType: 'Lung Volumes',
      render: (item: (typeof stores)[number]) => item?.lungVolumeStore?.getParamValue('TLC')?.result,
    },
    {
      id: 'FRC',
      name: 'FRC',
      unit: 'L',
      testType: 'Lung Volumes',
      render: (item: (typeof stores)[number]) => item?.lungVolumeStore?.getParamValue('FRC')?.result,
    },
    {
      id: 'RV',
      name: 'RV',
      unit: 'L',
      testType: 'Lung Volumes',
      render: (item: (typeof stores)[number]) => item?.lungVolumeStore?.getParamValue('RV')?.result,
    },
    {
      id: 'RV/TLC (%)',
      name: 'RV/TLC (%)',
      testType: 'Lung Volumes',
      render: (item: (typeof stores)[number]) => item?.lungVolumeStore?.getParamValue('RV/TLC')?.result,
    },
    {
      id: 'IC',
      name: 'IC',
      unit: 'L',
      testType: 'Lung Volumes',
      render: (item: (typeof stores)[number]) => item?.lungVolumeStore?.getParamValue('IC')?.result,
    },
    {
      id: 'ERV',
      name: 'ERV',
      unit: 'L',
      testType: 'Lung Volumes',
      render: (item: (typeof stores)[number]) => item?.lungVolumeStore?.getParamValue('ERV')?.result,
    },
    {
      id: 'LvVC',
      name: 'LvVC',
      unit: 'L',
      testType: 'Lung Volumes',
      render: (item: (typeof stores)[number]) => item?.lungVolumeStore?.getParamValue('LvVC')?.result,
    },
    // MRPs
    {
      id: 'Test Type 4',
      name: 'MRPs',
      testType: 'MRPs',
    },
    {
      id: 'MIP',
      name: 'MIP',
      unit: 'cm H20',
      testType: 'MRPs',
      render: (item: (typeof stores)[number]) => item?.mrpsStore?.getParamValue('MIP')?.result,
    },
    {
      id: 'MEP',
      name: 'MEP',
      unit: 'cm H20',
      testType: 'MRPs',
      render: (item: (typeof stores)[number]) => item?.mrpsStore?.getParamValue('MEP')?.result,
    },
    {
      id: 'SNIP',
      name: 'SNIP',
      unit: 'cm H20',
      testType: 'MRPs',
      render: (item: (typeof stores)[number]) => item?.mrpsStore?.getParamValue('SNIP')?.result,
    },

    // Exhaled Nitric Oxide
    {
      id: 'Test Type 5',
      name: 'Exhaled Nitric Oxide',
      testType: 'Exhaled Nitric Oxide',
    },
    {
      id: 'FeNO',
      name: 'FeNO',
      unit: 'ppb',
      testType: 'Exhaled Nitric Oxide',
      render: (item: (typeof stores)[number]) => item?.exhaledNitricOxideStore?.getParamValue('FeNO')?.result,
    },

    // Notes and Reports
    {
      id: 'Technical Note',
      name: 'Technical Note',
      render: (item: (typeof stores)[number]) => item?.test?.technicalnotes,
    },
    {
      id: 'Report',
      name: 'Report',
      render: (item: (typeof stores)[number]) => item?.test?.report_text,
    },
  ];

  return {stores: sortedStores, isLoading: isRespiratoryTrendLoading, rftHeaders};
}

export default UseComputeRftTrendData;
