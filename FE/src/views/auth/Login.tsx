import {Link, Navigate, useNavigate} from 'react-router';

import {useLocalStorage} from '@mantine/hooks';
import {observer} from 'mobx-react-lite';

import {Paths} from '@/api-types/routePaths.ts';
import {EmailIconly} from '@/components/icons/EmailIconly';
import {UserIconly} from '@/components/icons/UserIconly';
import {Input, Label} from '@/components/ui/Field.tsx';
import {Button} from '@/components/ui/button';
import {FieldError, Form, FormRootErrors, TextFormField} from '@/components/ui/form';
import {apiQueryOptions} from '@/hooks/use-api-query.ts';
import {queryClient} from '@/query-client.ts';
import authStore, {AuthStatus} from '@/store/auth.store.ts';

const LoginPage = observer(() => {
  const navigate = useNavigate();

  const [siteId] = useLocalStorage({
    key: 'rezibase:site_id',
    getInitialValueInEffect: false,
  });

  if (authStore.status === AuthStatus.LOGGED_IN) {
    return (
      <Navigate
        to="/"
        replace
      />
    );
  }

  return (
    <div className="flex min-h-screen w-full">
      <div className="flex w-full flex-col items-center justify-center px-10">
        <div className="min-w-88">
          <div className="mb-8">
            <img
              src="/dark-logo.png"
              alt="Rezibase"
              className="absolute top-5 left-5 h-6"
            />
          </div>

          <h1 className="mb-3 text-center text-[32px] leading-6 font-bold text-neutral-900">Welcome Back!</h1>
          <p className="mb-10 text-center text-gray-700">Please enter your details</p>
          <Form
            action={Paths.LOGIN}
            method="POST"
            onSubmitSuccess={async (res) => {
              await authStore.login(res.data.access_token, res.data.refresh_token);

              const result = await queryClient.fetchQuery(apiQueryOptions(Paths.SITES_LIST));

              if (!siteId || !result.find((site) => site.value.toString() === siteId.toString())) {
                localStorage.setItem('rezibase:site_id', String(result[0]?.value));
              }

              return navigate('/');
            }}
          >
            <div className="space-y-6">
              <FormRootErrors />

              <div className="space-y-1">
                <Label htmlFor="email">Email</Label>
                <TextFormField
                  name="email"
                  pattern="email"
                  required
                >
                  <div className="relative">
                    <div className="peer-focus:text-brand2-500 pointer-events-none absolute top-2.25 left-4 z-50 text-neutral-500">
                      <EmailIconly className="h-5 w-5" />
                    </div>
                    <Input
                      id="email"
                      type="email"
                      size="lg"
                      placeholder="Enter your email address"
                      className="peer react-aria-Input min-w-60 rounded-sm pl-11 text-neutral-800"
                    />
                  </div>

                  <FieldError />
                </TextFormField>
              </div>

              <div className="space-y-1">
                <Label htmlFor="password">Password</Label>
                <TextFormField
                  name="password"
                  required
                >
                  <div className="relative">
                    <div className="peer-focus:text-brand2-500 pointer-events-none absolute top-2.25 left-4 z-50 text-neutral-500">
                      <UserIconly className="h-5 w-5" />
                    </div>
                    <Input
                      id="password"
                      type="password"
                      placeholder="Enter your password"
                      size="lg"
                      className="react-aria-Input min-w-60 rounded-sm pl-11 text-neutral-800"
                    />
                  </div>

                  <FieldError />
                </TextFormField>
                <div className="flex justify-end">
                  <Link
                    to="/auth/forget-pass"
                    className="block w-max text-right text-sm font-medium text-neutral-800 hover:underline"
                  >
                    Forgot password?
                  </Link>
                </div>
              </div>

              <Button
                type="submit"
                className="h-10 w-full rounded-sm"
              >
                Login
              </Button>
            </div>
          </Form>
        </div>

        <p className="text-center text-sm text-gray-700 mt-10">
          Not a member yet?{' '}
          <Link
            className="text-brand-500 hover:underline"
            to="/auth/register"
            rel="noopener noreferrer"
          >
            Register for self guided free trail
          </Link>.
          <br />
          No credit card required.
        </p>
      </div>

      <div className="hidden w-full max-w-180 shrink-0 bg-white p-2 md:block md:w-[60%]">
        <div
          className="relative h-full w-full rounded-xl bg-cover bg-center"
          style={{
            backgroundImage: "url('/login.png')",
            backgroundPosition: 'right',
          }}
        >
          <div
            className="absolute inset-0 rounded-xl"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.4)', // 40% black overlay
            }}
          ></div>
        </div>
      </div>
    </div>
  );
});
export default LoginPage;
