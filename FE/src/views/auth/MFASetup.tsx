import {useEffect, useState} from 'react';
import {useNavigate} from 'react-router';

import {QRCodeSVG} from 'qrcode.react';

import {axiosInstance} from '@/axios.ts';
import {Input} from '@/components/ui/Field';
import {Button} from '@/components/ui/button';
import {Card} from '@/components/ui/card';
import {FieldError, Form, FormRootErrors, TextFormField} from '@/components/ui/form';

interface TOTPConfig {
  url: string;
  secret: string;
  algorithm: string;
  digits: number;
  period: number;
}

export default function MFASetupPage() {
  const [config, setConfig] = useState<TOTPConfig | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    axiosInstance.post('/api/user/mfa/enable/totp/').then((response) => {
      setConfig(response.data);
    });
  }, []);

  const onFormSubmit = (value: {token: string}) => {
    return axiosInstance.post('/api/user/mfa/confirm/totp/', value).then(() => {
      navigate('/auth/mfa/codes');
    });
  };

  return (
    <div className="flex min-h-screen w-full items-center bg-neutral-50">
      <div className="container mx-auto max-w-lg">
        <Card className="rounded-lg p-6 shadow-md">
          <Form onSubmit={onFormSubmit}>
            <h1 className="text-lg font-semibold">2 Factor Authentication</h1>
            <p className="mt-1 mb-4 text-sm text-neutral-800">
              For security reasons, we require you to enable two-factor authentication.
            </p>
            <p className="mt-1 mb-4 text-sm text-neutral-800">
              Scan the QR code below with your authenticator app, or click the button to show the text code.
            </p>

            {config && (
              <div className="my-6">
                <QRCodeSVG
                  className="mx-auto"
                  value={config.url}
                />
              </div>
            )}

            <p className="my-4 mt-1 text-sm text-neutral-800">
              If you can't scan the QR code above enter the text code instead.
            </p>

            <pre className="overflow-x-auto rounded bg-neutral-100 p-3 text-sm [&>:nth-child(4n+1)]:pl-1.5">
              {config?.secret.split('').map((e) => <span key={e}>{e}</span>)}
            </pre>

            <div className="space-y-4">
              <div>
                <h3 className="mt-4 mb-1 font-semibold">Enter the code from application</h3>
                <p className="text-sm text-neutral-800">
                  Open your two-factor authenticator app and enter the 6-digit code shown for Rezibase.
                </p>
              </div>

              <FormRootErrors />

              <TextFormField
                name="token"
                required
              >
                <div className="relative">
                  <Input
                    inputMode="numeric"
                    pattern="[0-9]{6}"
                    autoComplete="one-time-code"
                    placeholder="Enter 6-digit code"
                    className="w-full"
                  />
                </div>
                <FieldError />
              </TextFormField>

              <Button
                type="submit"
                className="w-full"
              >
                Verify and Enable
              </Button>
            </div>
          </Form>
        </Card>
      </div>
    </div>
  );
}
