import {useNavigate} from 'react-router';
import {useSearchParams} from 'react-router';

import {observer} from 'mobx-react-lite';

import {Paths} from '@/api-types/routePaths.ts';
import {Input, Label} from '@/components/ui/Field.tsx';
import {Button} from '@/components/ui/button';
import {FieldError, Form, FormRootErrors, TextFormField} from '@/components/ui/form';
import authStore from '@/store/auth.store.ts';

export const ResetPassword = observer(() => {
  const navigate = useNavigate();

  const [searchParams] = useSearchParams();
  const token = searchParams.get('token');

  return (
    <div className="flex min-h-screen w-full">
      <div className="flex w-full flex-col items-center justify-center px-10">
        <div className="min-w-88">
          <div className="mb-8">
            <img
              src="/dark-logo.png"
              alt="Rezibase"
              className="absolute top-5 left-5 h-6"
            />
          </div>

          <h1 className="mb-3 text-center text-[32px] leading-6 font-bold text-neutral-900">
            Set your new password!
          </h1>
          <p className="mb-10 text-center text-gray-700">Please enter a new password for your account.</p>

          {!token && (
            <Form
              action={Paths.CHANGE_PASS}
              method="POST"
              transformData={(data, form) => {
                if (data['new_password'] !== data['new_password2']) {
                  form.setError('new_password2', {
                    message: 'Password Mismatch',
                  });
                  return undefined;
                }
                return data;
              }}
              onSubmitSuccess={async () => {
                await authStore.validateTokenOnMount();
                return navigate('/');
              }}
            >
              <div className="space-y-6">
                <FormRootErrors />

                {authStore.user?.password_reset_required !== true && (
                  <TextFormField
                    name="current_password"
                    required
                  >
                    <Label>Current Password</Label>
                    <div className="relative">
                      <Input
                        id="current_password"
                        type="password"
                        placeholder="Enter your current password"
                        size="lg"
                        className="react-aria-Input min-w-60 rounded-sm text-neutral-800"
                      />
                    </div>

                    <FieldError />
                  </TextFormField>
                )}

                <TextFormField
                  name="new_password"
                  required
                >
                  <Label>New Password</Label>
                  <div className="relative">
                    <Input
                      id="new_password"
                      type="password"
                      placeholder="Enter your new password"
                      size="lg"
                      className="react-aria-Input min-w-60 rounded-sm text-neutral-800"
                    />
                  </div>

                  <FieldError />
                </TextFormField>

                <TextFormField
                  name="new_password2"
                  required
                >
                  <Label>Confirm New Password</Label>
                  <div className="relative">
                    <Input
                      id="new_password2"
                      type="password"
                      placeholder="Confirm password"
                      size="lg"
                      className="react-aria-Input min-w-60 rounded-sm text-neutral-800"
                    />
                  </div>

                  <FieldError />
                </TextFormField>

                <Button
                  type="submit"
                  className="h-10 w-full rounded-sm"
                >
                  Set Password
                </Button>
              </div>
            </Form>
          )}

          {token && (
            <Form
              action={Paths.RESET_PASS}
              method="POST"
              transformData={(data, form) => {
                if (data['new_password'] !== data['new_password2']) {
                  form.setError('new_password2', {
                    message: 'Password Mismatch',
                  });
                  return undefined;
                }
                return {password: data['new_password'], token: token};
              }}
              onSubmitSuccess={async () => {
                return navigate('/');
              }}
            >
              <div className="space-y-6">
                <FormRootErrors />

                <TextFormField
                  name="new_password"
                  required
                >
                  <Label>New Password</Label>
                  <div className="relative">
                    <Input
                      id="new_password_token"
                      type="password"
                      placeholder="Enter your new password"
                      size="lg"
                      className="react-aria-Input min-w-60 rounded-sm text-neutral-800"
                    />
                  </div>

                  <FieldError />
                </TextFormField>

                <TextFormField
                  name="new_password2"
                  required
                >
                  <Label>Confirm New Password</Label>
                  <div className="relative">
                    <Input
                      id="new_password2_token"
                      type="password"
                      placeholder="Confirm password"
                      size="lg"
                      className="react-aria-Input min-w-60 rounded-sm text-neutral-800"
                    />
                  </div>

                  <FieldError />
                </TextFormField>

                <Button
                  type="submit"
                  className="h-10 w-full rounded-sm"
                >
                  Set Password
                </Button>
              </div>
            </Form>
          )}
        </div>
      </div>

      <div className="hidden w-full max-w-180 shrink-0 bg-white p-2 md:block md:w-[60%]">
        <div
          className="relative h-full w-full rounded-xl bg-cover bg-center"
          style={{
            backgroundImage: 'url(\'/login.png\')',
            backgroundPosition: 'right',
          }}
        >
          <div
            className="absolute inset-0 rounded-xl"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.4)', // 40% black overlay
            }}
          ></div>
        </div>
      </div>
    </div>
  );
});
