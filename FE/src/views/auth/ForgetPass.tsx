import {useState} from 'react';
import {Link} from 'react-router';

import {Paths} from '@/api-types/routePaths';
import {EmailIconly} from '@/components/icons/EmailIconly';
import {Input, Label} from '@/components/ui/Field.tsx';
import {Button} from '@/components/ui/button';
import {FieldError, Form, FormRootErrors, TextFormField} from '@/components/ui/form';

export default function ForgetPassPage() {
  const [isResetSuccessful, setIsResetSuccessful] = useState(false);

  return (
    <div className="flex min-h-screen w-full">
      <div className="flex w-full flex-col items-center justify-center px-10">
        <div className="min-w-88">
          <div className="mb-8">
            <img
              src="/dark-logo.png"
              alt="Rezibase"
              className="absolute top-5 left-5 h-6"
            />
          </div>
          {!isResetSuccessful &&<>
            <h1 className="mb-3 text-center text-[32px] leading-6 font-bold text-neutral-900">
              Forgot Password?
            </h1>
            <p className="mb-10 text-center text-gray-700">Enter your email to reset your password</p>
          </>}
          {isResetSuccessful && (
            <div className="flex flex-col gap-6">
              <div className="rounded-sm p-6 text-center text-neutral-900 text-sm font-medium">
                We've sent you an email with a confirmation link. <br />
                Please follow the instructions to complete your password reset.
              </div>
            </div>
          )}

          {!isResetSuccessful && (
            <Form
              action={Paths.FORGET_PASS}
              method="POST"
              onSubmitSuccess={() => setIsResetSuccessful(true)}
            >
              <div className="space-y-6">
                <FormRootErrors />

                <div className="space-y-1">
                  <Label htmlFor="email">Email</Label>
                  <TextFormField
                    name="email"
                    pattern="email"
                    required
                  >
                    <div className="relative">
                      <div className="peer-focus:text-brand2-500 pointer-events-none absolute top-2.25 left-4 z-50 text-neutral-500">
                        <EmailIconly className="h-5 w-5" />
                      </div>
                      <Input
                        id="email"
                        type="email"
                        size="lg"
                        placeholder="Enter your email address"
                        className="peer react-aria-Input min-w-60 rounded-sm pl-11 text-neutral-800"
                      />
                    </div>

                    <FieldError />
                  </TextFormField>
                </div>

                <Button
                  type="submit"
                  className="h-10 w-full rounded-sm"
                >
                  Reset Password
                </Button>
              </div>
            </Form>
          )}
          <div className="mt-6 text-center text-sm text-neutral-900">
            Remember your password?{' '}
            <Link
              to="/auth/login"
              className="text-brand-500 font-semibold hover:underline"
            >
              Back to Login
            </Link>
          </div>
        </div>
      </div>

      <div className="hidden w-full max-w-180 shrink-0 bg-white p-2 md:block md:w-[60%]">
        <div
          className="relative h-full w-full rounded-xl bg-cover bg-center"
          style={{
            backgroundImage: "url('/login.png')",
            backgroundPosition: 'right',
          }}
        >
          <div
            className="absolute inset-0 rounded-xl"
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.4)', // 40% black overlay
            }}
          ></div>
        </div>
      </div>
    </div>
  );
}
