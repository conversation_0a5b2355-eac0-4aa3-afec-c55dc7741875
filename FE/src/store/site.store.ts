import {makeAutoObservable} from 'mobx';

import {QueryResultType} from '@/@types/graphql.tada.ts';
import {PropertiesOnly} from '@/@types/utils.ts';
import {getSiteWithSettings} from '@/graphql/site.ts';
import {applyMixins} from '@/lib/mixin.ts';
import {DisposableStoreMixin} from '@/store/disposable.store.ts';
import {assignDefinedProps, fetchAndObserveQuery} from '@/store/utils.ts';

interface KnownSettings {
  site_institution: string;
  site_state: string;
  site_logo: string;
  site_logo_showonreports: 'Y' | 'N';
  pas_mode_local: 'Y' | 'N';
  emr_mode_active: 'Y' | 'N';
  healthservice_code_primary: string;
  accreditation_logo_showonreports: 'Y' | 'N';
  accreditation_logo_resourcename?: string;
  site_country: string;
  import_rft_results_enabled: 'Y' | 'N';
  site_parameter_units: 'traditional' | 'si';
  autoreport_algorithm: 'ats_ers_2021' | 'ats_ers_2005';
  cpet_pred_selection: 'Kingsmill2024';
  site_logo_positioning?: {width?: number; height?: number; left?: number; top?: number; textflow?: string};
  accreditation_logo_positioning?: {
    width?: number;
    height?: number;
    left?: number;
    top?: number;
    textflow?: string;
  };
  report_options?: {
    suppress_fer_pcmpv_value_rft_report?: boolean;
    print_authorised_on_reports?: boolean;
    print_authorised_suppress_no_data?: boolean;
    print_verified_on_reports?: boolean;
    print_verified_suppress_no_data?: boolean;
    fer_calc_preats_ers2021?: boolean;
    fer_calc_ats_ers2021?: boolean;
    fer_calc_changeover_date?: string;
  };
}

export class SiteStore {
  id!: number;
  name?: string;
  enable_otp?: boolean;

  settings: SiteSetting[] = [];

  autoFetch: boolean = true;

  constructor(props: Partial<PropertiesOnly<SiteStore>>) {
    makeAutoObservable(this);
    assignDefinedProps(this, props as any);

    if (this.id && this.autoFetch) {
      this.init();
    }
  }

  init() {
    const subscription = fetchAndObserveQuery(
      {
        query: getSiteWithSettings,
        variables: {id: this.id},
      },
      (data) => {
        if (data?.site[0]) {
          this.fromQueryResult(data?.site[0]);
        }
      }
    );
    this.disposers.push(() => subscription.unsubscribe());
  }

  fromQueryResult(result: QueryResultType<typeof getSiteWithSettings>['site'][number]) {
    assignDefinedProps(this, result as any);

    if (result.site_settings) {
      this.settings = result.site_settings.map((s) => new SiteSetting(s as any));
    } else {
      this.settings = [];
    }
  }

  getConfig<K extends keyof KnownSettings | (string & {})>(
    name: K
  ): SiteSetting<K>['value'] | SiteSetting<K>['value_json'] {
    const setting = this.settings.find((e) => e.name === name);
    return (setting?.value ?? setting?.value_json) as any;
  }
}

export class SiteSetting<K extends string = string> {
  id!: number;
  name!: K;
  description?: string | null;
  value?: K extends keyof KnownSettings
    ? KnownSettings[K] extends string
      ? KnownSettings[K]
      : never
    : string;
  value_json?: K extends keyof KnownSettings
    ? KnownSettings[K] extends string
      ? never
      : KnownSettings[K]
    : Record<string, any>;

  constructor(props: Partial<PropertiesOnly<SiteSetting>>) {
    makeAutoObservable(this);
    assignDefinedProps(this, props as any);
  }

  get bool() {
    return this.value?.toLowerCase() === 'y';
  }
}

export interface SiteStore extends DisposableStoreMixin {}

applyMixins(SiteStore, [DisposableStoreMixin]);
