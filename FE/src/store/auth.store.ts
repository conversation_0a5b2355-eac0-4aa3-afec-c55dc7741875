import {jwtDecode, type JwtPayload} from 'jwt-decode';
import {autorun, makeAutoObservable, runInAction, untracked} from 'mobx';

import {Paths} from '@/api-types/routePaths.ts';
import {ApiResponse} from '@/api-types/utils';
import {axiosInstance, setHeaders} from '@/axios.ts';
import {SiteStore} from '@/store/site.store.ts';
import {apolloClient} from '@/apollo-client';

const ACCESS_TOKEN_KEY = 'rezibase:access_token';
const REFRESH_TOKEN_KEY = 'rezibase:refresh_token';

interface LoginMessage {
  type: 'logout' | 'login' | 'token_refresh';
  accessToken: string;
  refreshToken: string;
}

export enum AuthStatus {
  LOGGED_OUT = 'logged_out',
  VALIDATING = 'validating',
  LOGGED_IN = 'logged_in',
}

export interface JWTPayload extends JwtPayload {
  mfaRequired?: boolean;
  'https://hasura.io/jwt/claims': {
    'X-Hasura-Default-Role': string;
    'X-Hasura-Allowed-Roles': string[];
    'X-Hasura-Allowed-Sites': string;
    'X-Hasura-User-Id': string;
    'X-Hasura-Site-Id': string;
  };
}

class AuthStore {
  accessToken = localStorage.getItem(ACCESS_TOKEN_KEY) || null;
  refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY) || null;
  channel = new BroadcastChannel('auth-channel');
  status: AuthStatus = localStorage.getItem(ACCESS_TOKEN_KEY) ? AuthStatus.VALIDATING : AuthStatus.LOGGED_OUT;
  site?: SiteStore;
  user?: ApiResponse<typeof Paths.PROFILE>;
  isSwitchingSite = false;
  isRefreshingToken = false;

  constructor() {
    makeAutoObservable(this);
    this.setupChannelListener();
    this.validateTokenOnMount();

    autorun(() => {
      untracked(() => this.site)?.dispose();

      if (!this.tokenSiteId) {
        this.site = undefined;
      } else {
        this.site = new SiteStore({id: this.tokenSiteId});
      }
    });
  }

  get tokenPayload() {
    if (!this.accessToken) return undefined;
    try {
      return jwtDecode<JWTPayload>(this.accessToken);
    } catch (e) {
      return undefined;
    }
  }

  get tokenSiteId() {
    const siteId = this.tokenPayload?.['https://hasura.io/jwt/claims']?.['X-Hasura-Site-Id'];
    return siteId ? +siteId : undefined;
  }

  get requireMfa() {
    if (this.isSwitchingSite || this.isRefreshingToken || this.status !== AuthStatus.LOGGED_IN) {
      return false;
    }
    return this.tokenPayload?.mfaRequired;
  }

  get requireMfaSetup() {
    if (this.isSwitchingSite || this.isRefreshingToken || this.status !== AuthStatus.LOGGED_IN || !this.site) {
      return false;
    }

    // this.site.enable_otp === true
    return this.user?.totp_enabled === false;
  }

  get requirePasswordReset() {
    if (this.isSwitchingSite || this.isRefreshingToken || this.status !== AuthStatus.LOGGED_IN) {
      return false;
    }

    return this.user?.password_reset_required;
  }

  get requireLogin() {
    return !this.refreshToken && !this.accessToken && !this.isRefreshingToken && !this.isSwitchingSite;
  }

  async login(accessToken: string, refreshToken: string) {
    try {
      runInAction(() => {
        this.status = AuthStatus.VALIDATING;
        this.isSwitchingSite = false;
        this.isRefreshingToken = false;
      });

      const response = await axiosInstance.get(Paths.PROFILE, {
        headers: {Authorization: `Bearer ${accessToken}`},
      });

      setHeaders(accessToken);

      runInAction(() => {
        this.user = response.data;
        this.status = AuthStatus.LOGGED_IN;
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
        localStorage.setItem(ACCESS_TOKEN_KEY, accessToken);
        localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken);
      });

      this.channel.postMessage({type: 'login', accessToken, refreshToken});

    } catch (error) {
      console.error('Login token validation failed', error);
      this.logout();
    }
  }

  logout() {
    setHeaders();
    runInAction(() => {
      this.status = AuthStatus.LOGGED_OUT;
      this.accessToken = null;
      this.refreshToken = null;
      this.isSwitchingSite = false;
      this.isRefreshingToken = false;
      this.user = undefined;
      localStorage.removeItem(ACCESS_TOKEN_KEY);
      localStorage.removeItem(REFRESH_TOKEN_KEY);
      localStorage.removeItem('rezibase:last_visited');
      localStorage.removeItem('rezibase:site_id');
    });
    try {
      apolloClient.resetStore();
    } catch (e) {
      console.warn('Apollo client reset failed:', e);
    }

    this.channel.postMessage({type: 'logout', accessToken: '', refreshToken: ''});
  }

  async setupChannelListener() {
    this.channel.onmessage = async (msg: MessageEvent<LoginMessage>) => {
      if (msg.data.type === 'login') {
        try {
          runInAction(() => {
            this.status = AuthStatus.VALIDATING;
          });

          // Validate token in other tabs
          const response = await axiosInstance.get(Paths.PROFILE, {
            headers: {Authorization: `Bearer ${msg.data.accessToken}`},
          });

          setHeaders(msg.data.accessToken);

          runInAction(() => {
            this.user = response.data;
            this.status = AuthStatus.LOGGED_IN;
            this.accessToken = msg.data.accessToken;
            this.refreshToken = msg.data.refreshToken;
            localStorage.setItem(ACCESS_TOKEN_KEY, msg.data.accessToken);
            localStorage.setItem(REFRESH_TOKEN_KEY, msg.data.refreshToken);
            this.isSwitchingSite = false;
            this.isRefreshingToken = false;
          });

        } catch (error) {
          console.error('Token validation failed in other tab', error);
          this.logout();
        }
      } else if (msg.data.type === 'logout') {
        runInAction(() => {
          this.status = AuthStatus.LOGGED_OUT;
          this.accessToken = null;
          this.refreshToken = null;
          this.isSwitchingSite = false;
          this.isRefreshingToken = false;
          this.user = undefined;
          localStorage.removeItem(ACCESS_TOKEN_KEY);
          localStorage.removeItem(REFRESH_TOKEN_KEY);
        });
      } else if (msg.data.type === 'token_refresh') {
        runInAction(() => {
          this.accessToken = msg.data.accessToken;
          this.refreshToken = msg.data.refreshToken;
          localStorage.setItem(ACCESS_TOKEN_KEY, msg.data.accessToken);
          localStorage.setItem(REFRESH_TOKEN_KEY, msg.data.refreshToken);
        });
        setHeaders(msg.data.accessToken);
      }
    };
  }

  async validateTokenOnMount() {
    try {
      if (this.accessToken) {
        runInAction(() => {
          this.status = AuthStatus.VALIDATING;
        });

        const response = await axiosInstance.get(Paths.PROFILE, {
          headers: {Authorization: `Bearer ${this.accessToken}`},
        });

        runInAction(() => {
          this.user = response.data;
          this.status = AuthStatus.LOGGED_IN;
          this.isSwitchingSite = false;
          this.isRefreshingToken = false;
        });
      }
    } catch (error) {
      console.error('Initial token validation failed', error);
      if (this.refreshToken) {
        console.log('Will attempt token refresh via axios interceptor');
      } else {
        this.logout();
      }
    }
  }

  async switchSite(siteId: number) {
    try {
      runInAction(() => {
        this.status = AuthStatus.VALIDATING;
        this.isSwitchingSite = true;
      });

      const response = await axiosInstance.post(Paths.SWITCH_SITE, {site_id: siteId});
      const {access_token: accessToken} = response.data;

      if (!accessToken) {
        throw new Error('No access token returned from site switch');
      }

      setHeaders(accessToken);

      runInAction(() => {
        this.status = AuthStatus.LOGGED_IN;
        this.accessToken = accessToken;
        localStorage.setItem(ACCESS_TOKEN_KEY, accessToken);
        localStorage.setItem('rezibase:site_id', String(siteId));
        this.isSwitchingSite = false;
      });

      this.channel.postMessage({
        type: 'login',
        accessToken,
        refreshToken: this.refreshToken!
      });

      return true;

    } catch (error) {
      console.error('Site switch failed:', error);
      runInAction(() => {
        this.status = AuthStatus.LOGGED_IN;
        this.isSwitchingSite = false;
      });
      throw error;
    }
  }

  setAccessToken(accessToken: string) {
    setHeaders(accessToken);
    runInAction(() => {
      this.accessToken = accessToken;
      localStorage.setItem(ACCESS_TOKEN_KEY, accessToken);

      if (this.status !== AuthStatus.LOGGED_IN) {
        this.status = AuthStatus.LOGGED_IN;
      }

      this.isRefreshingToken = false;
    });

    this.channel.postMessage({
      type: 'token_refresh',
      accessToken,
      refreshToken: this.refreshToken || ''
    });
  }

  dispose() {
    this.channel.close();
  }
}

const authStore = new AuthStore();

export default authStore;