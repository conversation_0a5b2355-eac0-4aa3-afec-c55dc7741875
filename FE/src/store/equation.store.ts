import {makeAutoObservable} from 'mobx';

import {PropertiesOnly} from '@/@types/utils.ts';
import {apolloClient} from '@/apollo-client';
import {getEquation} from '@/features/equations-registry';
import {OutOfRangeResult, PredResult} from '@/features/equations-registry/types.ts';
import {EquationLoadMethod, getEthnicityCorrection} from '@/graphql/equations';
import {ParamEquationMetadata} from '@/store/parameter.ts';
import {assignDefinedProps} from '@/store/utils.ts';

import {EquationPref} from './equation-pref.store';

let math: typeof import('mathjs');

interface RMSVariables {
  age: number;
  ht: number;
  wt: number;
  _mpv?: number;
  _result?: number;
}

export enum ClipMethod {
  UseMinMaxValue = 1,
  DoNotCalculatePredicted = 2,
  Extrapolate = 3,
}

export type ParamEquationMetadatWithClipMethods = ParamEquationMetadata & {
  age_clip: number;
  age_clip_result: OutOfRangeResult;
  Htcm_clip: number;
  Htcm_clip_result: OutOfRangeResult;
  Wtkg_clip: number;
  Wtkg_clip_result: OutOfRangeResult;
};

export class Equation {
  id!: number;
  equationid!: number;
  test?: string;
  testid?: number;
  source?: string;
  sourceid?: number;
  parameter?: string;
  parameterid?: number;
  equation_mpv?: string;
  equation_range?: string;
  equation_zscore?: string;
  stattype_range?: string;
  stattype_rangeid?: number;
  equationtype?: 'RMS' | 'LMS';
  equationtypeid?: number;
  ethnicitycorrectiontype?: string;
  ethnicitycorrectiontypeid?: number;
  ethnicity?: string;
  ethnicityid?: number;
  gender?: string;
  genderid?: number;
  agegroup?: string;
  agegroupid?: number;
  age_lower?: string;
  age_upper?: string;
  ht_lower?: string;
  ht_upper?: string;
  wt_lower?: string;
  wt_upper?: string;
  lastedit?: string;
  lasteditby?: string;

  pref?: EquationPref;

  constructor(
    props: Partial<PropertiesOnly<Equation>>,
    public loadMethod?: EquationLoadMethod
  ) {
    makeAutoObservable(this);
    assignDefinedProps(this, props as any);

    if ((props as any).prefs_pred_new) {
      const pref = props as any;
      this.pref = new EquationPref(pref);
    }
  }

  get ageClipMethodId() {
    if (this.loadMethod === EquationLoadMethod.UseSourcesSpecified) return undefined;
    return this.pref?.age_clipmethodid;
  }

  get htClipMethodId() {
    if (this.loadMethod === EquationLoadMethod.UseSourcesSpecified) return undefined;
    return this.pref?.ht_clipmethodid;
  }

  get wtClipMethodId() {
    if (this.loadMethod === EquationLoadMethod.UseSourcesSpecified) return undefined;
    return this.pref?.wt_clipmethodid;
  }

  async calcPred(predMetadata: ParamEquationMetadata, testResult?: number): Promise<PredResult | undefined> {
    // Apply clip methods to the metadata
    const clippedMetadata = this.resolveClipLimitAction(predMetadata);

    if (this.equationtype?.toLowerCase() === 'rms') {
      return this.calcPredRMS(testResult, {
        age: clippedMetadata.age_clip,
        ht: clippedMetadata.Htcm_clip,
        wt: clippedMetadata.Wtkg_clip,
      });
    }
    if (this.equationtype?.toLowerCase() === 'lms' && this.equation_mpv) {
      const equationCalc = getEquation(this.equation_mpv);
      return equationCalc?.calcFn(this.parameter!, clippedMetadata, testResult);
    }
  }

  private async calcPredRMS(result: number | undefined, scope: Partial<RMSVariables>) {
    // Run MPV calculation and ethnicity correction in parallel
    const [mpv, correctionFactor] = await Promise.all([
      solveRmsEquation(this.equation_mpv, scope),
      this.getEthnicityCorrection(this.parameterid!, this.ethnicitycorrectiontypeid!, this.ethnicityid!),
    ]);
    const correctedMpv = mpv * correctionFactor;

    // Prepare all range calculations in parallel
    const rangePromises = [];
    if (this.stattype_range?.toLowerCase() === 'lln') {
      rangePromises.push(
        solveRmsEquation(this.equation_range, {...scope, _mpv: correctedMpv}).then((lln) => ({lln}))
      );
    }
    if (this.stattype_range?.toLowerCase() === 'uln') {
      rangePromises.push(
        solveRmsEquation(this.equation_range, {...scope, _mpv: correctedMpv}).then((uln) => ({uln}))
      );
    }
    if (this.stattype_range?.toLowerCase() === 'range') {
      const rangeEquations = this.equation_range?.split('[TO]');
      rangePromises.push(
        Promise.all([
          solveRmsEquation(rangeEquations?.[0], {...scope, _mpv: correctedMpv}),
          solveRmsEquation(rangeEquations?.[1], {...scope, _mpv: correctedMpv}),
        ]).then(([lower, upper]) => ({range: [lower, upper] as [number, number]}))
      );
    }

    // Calculate z-score if result is provided
    const zscorePromise = result
      ? solveRmsEquation(this.equation_zscore, {
          ...scope,
          _mpv: correctedMpv,
          _result: result,
        }).then((zscore) => ({zscore}))
      : Promise.resolve({});

    // Wait for all calculations to complete
    const [rangeResults, zscoreResult] = await Promise.all([Promise.all(rangePromises), zscorePromise]);

    // Combine all results
    const rangeResult = rangeResults.reduce((acc, curr) => ({...acc, ...curr}), {});

    return {
      mpv: correctedMpv,
      ...rangeResult,
      ...zscoreResult,
    };
  }

  private applyClipping(
    value: number,
    lowerBound: string | undefined,
    upperBound: string | undefined,
    clipMethod: ClipMethod | undefined
  ): {clippedValue: number; result: OutOfRangeResult} {
    const lowerLimit = Number(lowerBound);
    const upperLimit = Number(upperBound);

    // If value is within range
    if (value >= lowerLimit && value <= upperLimit) {
      return {
        clippedValue: value,
        result: OutOfRangeResult.inrange,
      };
    }

    // Value is out of range
    const isLow = value < lowerLimit;

    // If no clip method is defined
    if (!clipMethod) {
      return {
        clippedValue: 0,
        result: isLow ? OutOfRangeResult.low_no_clipmethod : OutOfRangeResult.high_no_clipmethod,
      };
    }

    // Apply clip method
    const outOfRangeResult = isLow ? OutOfRangeResult.low : OutOfRangeResult.high;

    switch (clipMethod) {
      case ClipMethod.UseMinMaxValue:
        return {
          clippedValue: isLow ? lowerLimit : upperLimit,
          result: outOfRangeResult,
        };
      case ClipMethod.DoNotCalculatePredicted:
        return {
          clippedValue: 0,
          result: outOfRangeResult,
        };
      case ClipMethod.Extrapolate:
        return {
          clippedValue: value,
          result: outOfRangeResult,
        };
      default:
        return {
          clippedValue: value,
          result: OutOfRangeResult.inrange,
        };
    }
  }

  private resolveClipLimitAction(metadata: ParamEquationMetadata): ParamEquationMetadatWithClipMethods {
    const result = {...metadata} as ParamEquationMetadatWithClipMethods;

    // Age clipping
    const ageClip = this.applyClipping(metadata.age, this.age_lower, this.age_upper, this.ageClipMethodId);
    result.age_clip = ageClip.clippedValue;
    result.age_clip_result = ageClip.result;

    // Height clipping
    const htClip = this.applyClipping(metadata.Htcm, this.ht_lower, this.ht_upper, this.htClipMethodId);
    result.Htcm_clip = htClip.clippedValue;
    result.Htcm_clip_result = htClip.result;

    // Weight clipping
    const wtClip = this.applyClipping(metadata.Wtkg, this.wt_lower, this.wt_upper, this.wtClipMethodId);
    result.Wtkg_clip = wtClip.clippedValue;
    result.Wtkg_clip_result = wtClip.result;

    return result;
  }

  private async getEthnicityCorrection(
    parameterId: number,
    correctionMethodId: number,
    ethnicityId: number
  ): Promise<number> {
    try {
      // If 'NIL' then return 1.0
      if (correctionMethodId === 2 || ethnicityId === 1) {
        return 1.0;
      }

      const {data} = await apolloClient.query({
        query: getEthnicityCorrection,
        variables: {
          parameterId,
          correctionMethodId,
        },
      });

      if (!data?.pred_ref_ethnicity_correctionfactors?.length) {
        return 1.0;
      }

      const factor = data.pred_ref_ethnicity_correctionfactors[0].factor;

      // Validate factor is numeric and within range
      if (typeof factor === 'number' && factor > 0 && factor < 2) {
        return factor;
      }

      return 1.0;
    } catch (error) {
      console.error('[EquationStore.getEthnicityCorrection] Error in getEthnicityCorrection:', error);
      return 1.0;
    }
  }
}

async function solveRmsEquation(equation: string | undefined, variables: Partial<RMSVariables>) {
  if (!equation || equation.toLowerCase() === '_na') {
    return NaN;
  }

  if (!math) {
    math = await import('mathjs/number');
  }

  try {
    const scope = {...variables};
    const result = math.evaluate(equation.toLowerCase(), scope);
    return typeof result === 'number' ? result : Number(result);
  } catch (error) {
    console.error('Error solving equation:', error);
    return NaN;
  }
}
