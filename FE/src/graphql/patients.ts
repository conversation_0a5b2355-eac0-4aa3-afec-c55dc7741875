import {graphql} from '@/graphql';

export const searchPatientsQuery = graphql(`
  query SearchPasPt($searchText: String!, $genderCodes: _text, $siteId: String, $limit: Int!, $offset: Int!) {
    search_pas_pt(
      args: {search_text: $searchText, gender_filter: $genderCodes, site_filter: $siteId}
      limit: $limit
      offset: $offset
      order_by: {lastupdated_by: desc}
    ) {
      ur
      firstname
      middlename
      surname
      dob
      phone_mobile
      email
      gender_code
      patientid
      title
    }
    search_pas_pt_aggregate(
      args: {search_text: $searchText, gender_filter: $genderCodes, site_filter: $siteId}
    ) {
      aggregate {
        count
      }
    }
  }
`);

export const getPatientsRSessionData = graphql(`
  query GetPatientsRSessionsData($patientId: Int) {
    r_sessions(where: {patientid: {_eq: $patientId}}) {
      sessionid
      patientid
      testdate
      lab
      height
      weight
      req_name
      req_address
      req_providernumber
      req_healthservice_text
      req_healthservice_code
      req_date
      req_time
      req_phone
      req_fax
      req_email
      req_clinicalnotes
      smoke_hx
      smoke_cigsperday
      smoke_yearssmoked
      smoke_packyears
      smoke_last
      diagnosticcategory
      pred_sourceids
      admissionstatus
      report_copyto
      report_copyto_2
      billing_billedto
      billing_billingmo
      billing_billingmoproviderno
      lastupdated_session
      lastupdatedby_session
    }
  }
`);

export const getPatientsRespiratoryLabData = graphql(`
  query GetPatientsRespiratoryLabData($testType: String!, $patientId: Int) {
    rft_routine(
      where: {
        patientid: {_eq: $patientId}
        testtype: {_ilike: $testType}
      }
    ) {
      id: rftid
      patientid
      sessionid
      lab
      testtime
      testtype
      report_status
    }
    prov_test(
      where: {
        patientid: {_eq: $patientId}
        testtype: {_ilike: $testType}
      }
    ) {
      id: provid
      patientid
      sessionid
      lab
      testtime
      testtype
      report_status
    }
    r_walktests_v1heavy(
      where: {
        patientid: {_eq: $patientId}
        testtype: {_ilike: $testType}
      }
    ) {
      id: walkid
      patientid
      sessionid
      lab
      testtime
      testtype
      report_status
    }
    r_cpet(
      where: {
        patientid: {_eq: $patientId}
        testtype: {_ilike: $testType}
      }
    ) {
      id: cpetid
      patientid
      sessionid
      lab
      testtime
      testtype
      report_status
    }
    r_spt(
      where: {
        patientid: {_eq: $patientId}
        testtype: {_ilike: $testType}
      }
    ) {
      id: sptid
      patientid
      sessionid
      lab
      testtime
      testtype
      report_status
    }
    r_hast(
      where: {
        patientid: {_eq: $patientId}
        testtype: {_ilike: $testType}
      }
    ) {
      id: hastid
      patientid
      sessionid
      lab
      testtime
      testtype
      report_status
    }
  }
`);

export const getPatientRespiratoryTrend = graphql(`
  query GetPatientRespiratoryTrend($patientId: Int!) {
    r_sessions(where: {patientid: {_eq: $patientId}}) {
      sessionid
      testdate
      height
      weight
      patientid
    }
    rft_routine(where: {patientid: {_eq: $patientId}}) {
      sessionid
      rftid
      testtime
      testtype
      r_pre_condition
      r_post_condition
      report_status
      r_bl_fev1
      r_bl_fvc
      r_bl_vc
      r_bl_fer
      r_bl_fef2575
      r_bl_pef

      r_post_fev1
      r_post_fvc
      r_post_vc
      r_post_fer
      r_post_fef2575
      r_post_pef

      r_bl_va
      r_bl_tlco
      r_bl_kco

      r_bl_hb

      r_bl_tlc
      r_bl_frc
      r_bl_rv
      r_bl_rvtlc

      r_bl_mip
      r_bl_mep
      r_bl_snip

      r_bl_feno

      technicalnotes
      report_text
    }
    prov_test(where: {patientid: {_eq: $patientId}}) {
      sessionid
      provid
      bdstatus
      testtime
      testtype
      report_status
      r_bl_fev1
      r_bl_fvc
      r_bl_vc
      r_bl_fer
      technicalnotes
      report_text
    }
  }
`);

export const getPatientsCPAPClinicData = graphql(`
  query GetPatientsCPAPClinicData($testType: String!, $patientId: Int) {
    cpap_visit_clinic(where: {patientid: {_eq: $patientId}, visit_type: {_ilike: $testType}}) {
      id: cpap_visit_clinic_id
      patientid
      testdate: visit_datetime
      testtime: visit_datetime
      testtype: visit_type
      report_status
    }
  }
`);

export const getPatientsSleepLabData = graphql(`
  query GetPatientsSleepFitLabData($testType: String!, $patientId: Int) {
    r_psg_imported_files(
      where: {
        patientid: {_eq: $patientId}
        description: {_ilike: $testType}
      }
    ) {
      id
      testtype: description
      filetype
      report_status
      testdate: display_date
      testtime: display_time
    }
    p_sleep_study(
      where: {
        patientid: {_eq: $patientId}
        test_type: {_ilike: $testType}
      }
    ) {
      testdate: study_date
      report_status
      id: test_id
      test_type
    }
  }
`);

export const getSleepStudyLabListTypes = graphql(`
  query GetSleepStudyLabListTypes($patientId: Int) {
    list_psg_types {
      id
      code
      description
      enabled
    }
  }
`);

export const getHealthServicesList = graphql(`
  query GetHealthServicesList {
    list_healthservices {
      description
      code
    }
  }
`);

export const getSiteConfigHealthServicesList = graphql(`
  query GetSiteConfigHealthServicesList {
    site_config_healthservices {
      id
      hsid
      enabled
    }
  }
`);

export const getPrimaryUr = graphql(`
  query GetPrimaryUr($ur_id: Int!) {
    pas_pt_ur_numbers(where: {ur_id: {_eq: $ur_id}}) {
      ur
      ur_hsid
      patientid
    }
  }
`);
export const updatePatientGender = graphql(`
  mutation UpdatePatientGender($patientId: Int!, $genderCode: String!) {
    update_pas_pt(
      where: {patientid: {_eq: $patientId}},
      _set: {gender_forrfts_code: $genderCode}
    ) {
      affected_rows
    }
  }
`);

export const updatePatientEthnicity = graphql(`
  mutation UpdatePatientGender($patientId: Int!, $raceCode: String!) {
    update_pas_pt(
      where: {patientid: {_eq: $patientId}},
      _set: {race_forrfts_code: $raceCode}
    ) {
      affected_rows
    }
  }
`);
export const getPatientsDetailData = graphql(`
  query GetPatientsDetailData($patientId: Int) {
    pas_pt(where: {patientid: {_eq: $patientId}}) {
      pas_pt_names {
        patientid
        name_type
        title
        firstname
        surname
        middlename
      }
      pas_pt_ur_numbers {
        patientid
        ur_id
        ur
        ur_hsid
        ur_status
        health_service {
          code
          description
        }
      }
      pas_pt_addresses {
        addressid
        address_1
        address_2
        address_type_code
        patientid
        postcode
        statename
        suburb
      }
      patientid
      aboriginalstatus_code
      countryofbirth_code
      death_date
      death_indicator
      dob
      email
      gender_code
      gender_forrfts_code
      lastupdated_by
      lastupdated_date
      medicare_expirydate
      medicare_no
      phone_home
      phone_mobile
      phone_work
      preferredlanguage_code
      race_code_notused
      race_forrfts_code
      research_discussed_by
      research_discussed_date
      research_discussed_outcome
      research_notes
      research_tags
      ur
      ur_hsid
      ur_id
    }
  }
`);

export const getEthnicities = graphql(`
  query GetEthnicities {
    pred_ref_ethnicities {
      id
      ethnicityid
      description
      list_option_for_demographics
    }
  }
`);

export const getRFTGenders = graphql(`
  query GetRFTGenders {
    pred_ref_genders(where: {list_option_for_rfts: {_eq: true}}) {
      id
      genderid
      list_option_for_rfts
      list_option_for_demographics
      description
      list_option_for_preds
      gender
      gender_code
    }
  }
`);

export const getPatientsAllLabCounts = graphql(`
  query GetPatientsAllLabCounts($patientId: Int) {
    r_fit_aggregate(where: {patientid: {_eq: $patientId}}) {
      aggregate {
        count
      }
    }

    cpap_visit_clinic_aggregate(where: {patientid: {_eq: $patientId}}) {
      aggregate {
        count
      }
    }

    r_psg_imported_files_aggregate(where: {patientid: {_eq: $patientId}}) {
      aggregate {
        count
      }
    }
    p_sleep_study_aggregate(where: {patientid: {_eq: $patientId}}) {
      aggregate {
        count
      }
    }

    rft_routine_aggregate(where: {patientid: {_eq: $patientId}}) {
      aggregate {
        count
      }
    }
    prov_test_aggregate(where: {patientid: {_eq: $patientId}}) {
      aggregate {
        count
      }
    }
    r_walktests_v1heavy_aggregate(where: {patientid: {_eq: $patientId}}) {
      aggregate {
        count
      }
    }
    r_cpet_aggregate(where: {patientid: {_eq: $patientId}}) {
      aggregate {
        count
      }
    }
    r_spt_aggregate(where: {patientid: {_eq: $patientId}}) {
      aggregate {
        count
      }
    }
    r_hast_aggregate(where: {patientid: {_eq: $patientId}}) {
      aggregate {
        count
      }
    }
  }
`);

export const demographicOptions = graphql(`
  query DemographicOptions {
    pred_ref_ethnicities {
      id
      description
      list_option_for_demographics
    }
    pred_ref_genders {
      id
      genderid
      list_option_for_rfts
      list_option_for_demographics
      description
      list_option_for_preds
      gender
      gender_code
    }

    list_healthservices {
      id
      description
      code
    }
    
    list_deathindicator {
      description
      code
    }
  }
`);

export const insert_session = graphql(`
  mutation InsertSession(
    $patientid: Int!
    $testdate: date
    $lab: String
    $height: String
    $weight: String
    $req_name: String
    $req_address: String
    $req_providernumber: String
    $req_healthservice_text: String
    $req_healthservice_code: String
    $req_date: date
    $req_time: time
    $req_phone: String
    $req_fax: String
    $req_email: String
    $req_clinicalnotes: String
    $smoke_hx: String
    $smoke_cigsperday: String
    $smoke_yearssmoked: String
    $smoke_packyears: String
    $smoke_last: String
    $diagnosticcategory: String
    $pred_sourceids: String
    $admissionstatus: String
    $report_copyto: String
    $report_copyto_2: String
    $billing_billedto: String
    $billing_billingmo: String
    $billing_billingmoproviderno: String
    $lastupdated_session: timestamp
    $lastupdatedby_session: String
  ) {
    insert_r_sessions_one(
      object: {
        patientid: $patientid
        testdate: $testdate
        lab: $lab
        height: $height
        weight: $weight
        req_name: $req_name
        req_address: $req_address
        req_providernumber: $req_providernumber
        req_healthservice_text: $req_healthservice_text
        req_healthservice_code: $req_healthservice_code
        req_date: $req_date
        req_time: $req_time
        req_phone: $req_phone
        req_fax: $req_fax
        req_email: $req_email
        req_clinicalnotes: $req_clinicalnotes
        smoke_hx: $smoke_hx
        smoke_cigsperday: $smoke_cigsperday
        smoke_yearssmoked: $smoke_yearssmoked
        smoke_packyears: $smoke_packyears
        smoke_last: $smoke_last
        diagnosticcategory: $diagnosticcategory
        pred_sourceids: $pred_sourceids
        admissionstatus: $admissionstatus
        report_copyto: $report_copyto
        report_copyto_2: $report_copyto_2
        billing_billedto: $billing_billedto
        billing_billingmo: $billing_billingmo
        billing_billingmoproviderno: $billing_billingmoproviderno
        lastupdated_session: $lastupdated_session
        lastupdatedby_session: $lastupdatedby_session
      }
      on_conflict: {constraint: pk_r_sessions}
    ) {
      sessionid
    }
  }
`);

export const insertRFTRoutine = graphql(`
  mutation InsertRFTRoutine($sessionId: Int!, $patientId: Int!, $testtime: time) {
    insert_rft_routine_one(
      object: {
        sessionid: $sessionId
        patientid: $patientId
        bdstatus: null
        device_info: null
        flowvolloop: null
        lab: null
        lastupdated_rft: null
        lastupdatedby_rft: null
        lungvolumes_method: null
        r_abg1_aapo2: null
        r_abg1_be: null
        r_abg1_cohb: null
        r_abg1_fio2: null
        r_abg1_hco3: null
        r_abg1_paco2: null
        r_abg1_pao2: null
        r_abg1_ph: null
        r_abg1_sampletype: null
        r_abg1_sao2: null
        r_abg1_shunt: null
        r_abg2_aapo2: null
        r_abg2_be: null
        r_abg2_cohb: null
        r_abg2_fio2: null
        r_abg2_hco3: null
        r_abg2_paco2: null
        r_abg2_pao2: null
        r_abg2_ph: null
        r_abg2_sampletype: null
        r_abg2_sao2: null
        r_abg2_shunt: null
        r_bl_erv: null
        r_bl_fef2575: null
        r_bl_feno: null
        r_bl_fer: null
        r_bl_fev1: null
        r_bl_frc: null
        r_bl_fvc: null
        r_bl_hb: null
        r_bl_ic: null
        r_bl_ivc: null
        r_bl_kco: null
        r_bl_lvvc: null
        r_bl_mep: null
        r_bl_mip: null
        r_bl_pef: null
        r_bl_rv: null
        r_bl_rvtlc: null
        r_bl_snip: null
        r_bl_tlc: null
        r_bl_tlco: null
        r_bl_va: null
        r_bl_vc: null
        r_condition_feno: null
        r_condition_lv: null
        r_condition_mrp: null
        r_condition_tl: null
        r_post_condition: null
        r_post_fef2575: null
        r_post_fer: null
        r_post_fev1: null
        r_post_fvc: null
        r_post_pef: null
        r_post_vc: null
        r_pre_condition: null
        r_spo2_1: null
        r_spo2_2: null
        report_amendedby: null
        report_amendeddate: null
        report_amendednotes: null
        report_authorisedby: null
        report_authoriseddate: null
        report_reportedby: null
        report_reporteddate: null
        report_status: "Unreported"
        report_text: null
        report_verifiedby: null
        report_verifieddate: null
        sample_type: null
        scientist: null
        technicalnotes: null
        testtime: $testtime
        testtype: "RFTs ()"
      }
    ) {
      rftid
    }
  }
`);

export const getRequestedMO = graphql(`
  query GetRequestedMO {
    rft_routine {
      id: rftid
      r_session {
        sessionid
        req_name
      }
    }
    prov_test {
      id: provid
      r_session {
        sessionid
        req_name
      }
    }
    r_walktests_v1heavy {
      id: walkid

      r_session {
        sessionid
        req_name
      }
    }
    r_cpet {
      id: cpetid

      r_session {
        sessionid
        req_name
      }
    }
    r_spt {
      id: sptid
      r_session {
        sessionid
        req_name
      }
    }
    r_hast{
      id: hastid

      r_session {
        req_name
      }
    }

    cpap_visit_clinic {
      id: cpap_visit_clinic_id
      req_name: clinician

    }

    p_sleep_study {
      id: test_id
      req_name: requested_by
    }
  }
`);

export const updateSessionHeight = graphql(`
  mutation UpdateSessionHeight($sessionId: Int!, $height: String!) {
    update_r_sessions_by_pk(
      pk_columns: { sessionid: $sessionId }
      _set: { height: $height }
    ) {
      sessionid
    }
  }
`);
export const updateSessionWeight = graphql(`
  mutation UpdateSessionWeight($sessionId: Int!, $weight: String!) {
    update_r_sessions_by_pk(
      pk_columns: { sessionid: $sessionId }
      _set: { weight: $weight }
    ) {
      sessionid
    }
  }
`);

export const update_session = graphql(`
  mutation UpdateSession(
    $sessionId: Int!
    $patientid: Int!
    $testdate: date
    $lab: String
    $height: String
    $weight: String
    $req_name: String
    $req_address: String
    $req_providernumber: String
    $req_healthservice_text: String
    $req_healthservice_code: String
    $req_date: date
    $req_time: time
    $req_phone: String
    $req_fax: String
    $req_email: String
    $req_clinicalnotes: String
    $smoke_hx: String
    $smoke_cigsperday: String
    $smoke_yearssmoked: String
    $smoke_packyears: String
    $smoke_last: String
    $diagnosticcategory: String
    $pred_sourceids: String
    $admissionstatus: String
    $report_copyto: String
    $report_copyto_2: String
    $billing_billedto: String
    $billing_billingmo: String
    $billing_billingmoproviderno: String
    $lastupdated_session: timestamp
    $lastupdatedby_session: String
  ) {
    update_r_sessions_by_pk(
      pk_columns: {sessionid: $sessionId}
      _set: {
        patientid: $patientid
        testdate: $testdate
        lab: $lab
        height: $height
        weight: $weight
        req_name: $req_name
        req_address: $req_address
        req_providernumber: $req_providernumber
        req_healthservice_text: $req_healthservice_text
        req_healthservice_code: $req_healthservice_code
        req_date: $req_date
        req_time: $req_time
        req_phone: $req_phone
        req_fax: $req_fax
        req_email: $req_email
        req_clinicalnotes: $req_clinicalnotes
        smoke_hx: $smoke_hx
        smoke_cigsperday: $smoke_cigsperday
        smoke_yearssmoked: $smoke_yearssmoked
        smoke_packyears: $smoke_packyears
        smoke_last: $smoke_last
        diagnosticcategory: $diagnosticcategory
        pred_sourceids: $pred_sourceids
        admissionstatus: $admissionstatus
        report_copyto: $report_copyto
        report_copyto_2: $report_copyto_2
        billing_billedto: $billing_billedto
        billing_billingmo: $billing_billingmo
        billing_billingmoproviderno: $billing_billingmoproviderno
        lastupdated_session: $lastupdated_session
        lastupdatedby_session: $lastupdatedby_session
      }
    ) {
      sessionid
    }
  }
`);

