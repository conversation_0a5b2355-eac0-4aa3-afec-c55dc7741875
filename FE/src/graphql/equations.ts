import { graphql } from '@/graphql.ts';


export enum EquationLoadMethod {
  UseCurrentPrefs = 0,
  UseSourcesInUseAtTestDate = 1,
  UseSourcesSpecified = 2,
}


export const getParamEquations = graphql(`
  query GetParamEquations(
    $paramId: Int!
    $genderId: Int!
    $age: numeric!
    $ethnicityId: Int!
    $method: Int!
    $testDate: timestamp!
    $testId: Int
    $sourceId: Int
  ) {
    sp_get_eq_for_parameter(
      args: {
        p_parameterid: $paramId
        p_gender_id: $genderId
        p_age: $age
        p_ethnicity_id: $ethnicityId
        p_method: $method
        p_test_date: $testDate
        p_test_id: $testId
        p_source_id: $sourceId
      }
    ) {
      equationid
      equationtypeid
      equationtype
      equation_mpv
      equation_range
      equation_zscore
      age_lower
      age_upper
      ht_lower
      ht_upper
      wt_lower
      wt_upper
      stattype_range
      stattype_rangeid
      sourceid
      parameterid
      parameter
      testid
      ethnicitycorrectiontype
      ethnicitycorrectiontypeid
      ethnicity
      ethnicityid
      
      prefs_pred_new {
        prefid
        equationid
        age_clipmethod
        age_clipmethodid
        ht_clipmethod
        ht_clipmethodid
        wt_clipmethod
        wt_clipmethodid
        active
        startdate
        enddate
        markfordeletion
        lastedit
        lasteditby
      }
    }
  }
`);

export const getStatTypes = graphql(`
  query GetStatTypes {
    pred_ref_stattypes {
      stattypeid
      stattype
      description
    }
  }
`);

export const getPredGenders = graphql(`
  query GetPredGenders {
    pred_ref_genders(where: {list_option_for_preds: {_eq: true}}) {
      id
      genderid
      list_option_for_rfts
      list_option_for_demographics
      description
      list_option_for_preds
      gender
      gender_code
    }
  }
`);

export const getAgeGroups = graphql(`
  query GetAgeGroups {
    pred_ref_agegroups {
      agegroupid
      agegroup
      description
    }
  }
`);

export const updateEquation = graphql(`
  mutation UpdateEquation($equationId: Int!, $equation: pred_equations_set_input!) {
    update_pred_equations_by_pk(pk_columns: {id: $equationId}, _set: $equation) {
      equationid
    }
  }
`);

export const createEquation = graphql(`
  mutation CreateEquation($equation: pred_equations_insert_input!) {
    insert_pred_equations_one(object: $equation) {
      equationid
    }
  }
`);

export const getEthnicityCorrection = graphql(`
  query GetEthnicityCorrection($parameterId: Int!, $correctionMethodId: Int!) {
    pred_ref_ethnicity_correctionfactors(
      where: {
        parameterid: {_eq: $parameterId},
        correctionmethodid: {_eq: $correctionMethodId}
      }
    ) {
      factorid
      parameter
      factor
    }
  }
`);
