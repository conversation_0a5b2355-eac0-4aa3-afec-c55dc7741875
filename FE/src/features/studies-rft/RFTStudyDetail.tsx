import clsx from 'clsx';
import {
  Suspense,
  useCallback,
  useEffect, useRef,
  useState,
} from 'react';
import {Switch} from 'react-aria-components';
import {
  useLocation,
  useNavigate,
  useParams,
  useSearchParams,
} from 'react-router';

import {useQuery, useMutation} from '@apollo/client';
import {Redo2, Undo2} from 'lucide-react';
import {observer} from 'mobx-react-lite';
import {getRFTGenders,getEthnicities, updatePatientGender,updatePatientEthnicity} from '@/graphql/patients';
import PaperIcon from '@/assets/iconly/Paper.svg?react';
import SearchChatIcon from '@/assets/iconly/SearchChat.svg?react';
import TrendDownGraphIcon from '@/assets/iconly/TrendDownGraph.svg?react';
import {Button} from '@/components/ui/button.tsx';
import {useSidebar} from '@/components/ui/sidebar.tsx';
import {
  Ta<PERSON>,
  Ta<PERSON>Content,
  Ta<PERSON>List,
  TabsTrigger,
} from '@/components/ui/tabs.tsx';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip.tsx';
import {useRftReport} from '@/features/studies-rft/report';
import {ReportSidebar} from '@/features/studies-rft/sections/report-sidebar.tsx';
import {RftStore} from '@/features/studies-rft/store/rtf.store.ts';
import {ReportPatientInfo} from '@/features/studies/ReportPatientInfo.tsx';
import {undoStack} from '@/features/undo-stack';
import {getPatientsDetailData, updateSessionWeight,updateSessionHeight} from '@/graphql/patients.ts';
import {useGlobalStoreLoader} from '@/store/global.store.ts';
import PatientTrendTable from '@/views/patients/patient-detail/patient-trend-view/components/patient-trend-table.tsx';

import {BloodGasesSection} from './sections/blood-gases.tsx';
import {COTransferSection} from './sections/co-transfer.tsx';
import {ExhaledNitricOxideSection} from './sections/exhaled-nitric-oxide.tsx';
import {FlowVolume} from './sections/flow-volume.tsx';
import {LungVolumesSection} from './sections/lung-volumes.tsx';
import {MRPsSection} from './sections/mrps.tsx';
import {SpirometryFormSection} from './sections/spirometry.tsx';
import './styles.css';
import {Image} from "@react-pdf/renderer";
import {toPng} from "html-to-image";
import {tw} from "@/lib/react-pdf-tailwind.ts";
import {useLocalStorage} from "@mantine/hooks";

function TestSections({rftStore}: {rftStore: RftStore}) {
  useGlobalStoreLoader();
  return (
    <div className="relative space-y-3 rounded border border-neutral-200 bg-white p-4">
      <SpirometryFormSection rftStore={rftStore} />
      <COTransferSection rftStore={rftStore} />
      <LungVolumesSection rftStore={rftStore} />
      <ExhaledNitricOxideSection rftStore={rftStore} />
      <MRPsSection rftStore={rftStore} />
      <BloodGasesSection rftStore={rftStore} />
      <FlowVolume rftStore={rftStore} />
    </div>
  );
}

const RFTStudyDetail = observer(() => {
  type UploadResponse = {
    success?: boolean;
    error?: string;
    message?: string;
    original?: any;
  };
  const [siteId] = useLocalStorage({
    key: 'rezibase:site_id',
    getInitialValueInEffect: false,
  });
  const {id, patientId} = useParams<{id: string; patientId: string}>();
  const {toggleSidebar, state} = useSidebar();
  const {createReport, isLoading} = useRftReport();
  const [searchParams] = useSearchParams();
  const editParam = searchParams.get('edit');
  const [isTrendViewActive, setIsTrendViewActive] = useState(false);
  const isEditing = editParam === 'true';
  const [confirmMessage, setConfirmMessage] = useState('');
  const [showFileMenu, setShowFileMenu] = useState(false);
  const [attachments, setAttachments] = useState<{ key: string; filename: string; s3_key?: string; }[]>([]);
  const [loadingFiles, setLoadingFiles] = useState(false);
  useQuery(getPatientsDetailData, {variables: {patientId: +patientId!}});

  const [rftStore, setRftStore] = useState(() => {
    return new RftStore({rftid: +id!, isEditing});
  });

  const [dragging, setDragging] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [pendingFile, setPendingFile] = useState<File | null>(null);
  const [pdfUploaded, setPdfUploaded] = useState(false);
  const [undoingUpload, setUndoingUpload] = useState(false);
  const [originalData, setOriginalData] = useState(null);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  useEffect(() => {
    undoStack.reset();
    setRftStore(new RftStore({rftid: +id!, isEditing}));
    return () => {
      rftStore.dispose();
      undoStack.reset();
    };
  }, []);

  useEffect(() => {
    if (state === 'expanded') {
      toggleSidebar();
    }
  }, []);

  const fetchAttachmentList = async () => {
    setLoadingFiles(true);
    try {
      const response = await fetch("/api/attachments/list", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          patient_id: patientId,
          rft_id: id,
        }),
      });
      const data = await response.json() as { files?: { key: string; filename: string; s3_key?: string }[] };
      if (Array.isArray(data.files)) {
        setAttachments(data.files);
      }
      else {
        setAttachments([]);
      }
    } catch (err) {
      console.error("Failed to load attachments", err);
      setAttachments([]);
    } finally {
      setLoadingFiles(false);
    }
  };

  const handleUndoUpload = async () => {
    if (!originalData) {
      setSuccessMessage('❌ No original data to revert to');
    }

    setUndoingUpload(true);
    try {
      const response = await fetch('/api/pdf_import/undo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          rft_id: id,
          original: originalData,
        }),
      });

      if (!response.ok) throw new Error('Undo failed');
      const result = await response.json() as UploadResponse;
      if (result.success) {
        setSuccessMessage('✅ PDF import undone');
        setPdfUploaded(false);
      } else {
        setSuccessMessage('❌ Failed to undo import');
      }
    } catch (err) {
      console.error(err);
      setSuccessMessage('❌ Error occurred during undo');
    } finally {
      setUndoingUpload(false);
      setTimeout(() => setSuccessMessage(''), 5000);
    }
  };
  const handleUploadAttachment = async (file: File) => {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("patient_id", patientId!);
    formData.append("rft_id", id!);
    formData.append("uploaded_by", '');
    formData.append("description", "Baseline spirometry report");
    formData.append("site_id", siteId!); // Replace with actual site ID if needed

    try {
      const res = await fetch("/api/attachments/upload", {
        method: "POST",
        body: formData,
      });

      const data = await res.json() as { success?: boolean };
      if (data.success) {
        await fetchAttachmentList(); // refresh file list
      }
    } catch (err) {
      console.error("Upload failed", err);
    }
  };

  const handleSubmit = useCallback(
    async (file: File, isOverwrite = false) => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('id', id!);
      formData.append('patientId', patientId!);
      formData.append('is_overwrite', String(isOverwrite));
      try {
        const response = await fetch('/api/pdf_import/patient', {
          method: 'POST',
          body: formData,
        });

        const result = await response.json() as UploadResponse;

        if (response.status === 200 && result.error === 'Patient mismatch') {
          setPendingFile(file);
          setConfirmMessage(result.message || 'Patient data does not match. Do you want to overwrite the existing patient data?');
          setConfirmOpen(true);
          return;
        }
        if (response.status === 200 && result.error === 'Data Overwrite Warning') {
          setPendingFile(file);
          setConfirmMessage(result.message || 'The existing data will be overwritten. Do you want to proceed?');
          setConfirmOpen(true);
          return;
        }

        if (!response.ok) throw new Error(result.error || 'Upload failed');
        if (result.success) {
          setOriginalData(result.original);  // Store original RFT data
          setSuccessMessage('✅ Patient data extracted');
          setPdfUploaded(true);
          await fetchAttachmentList();
        }
        setSuccessMessage('✅ Patient data extracted');
        setPdfUploaded(true);
      } catch (err) {
        console.error(err);
        setSuccessMessage('❌ Failed to extract patient data');
      } finally {
        setTimeout(() => setSuccessMessage(''), 5000);
      }
    },
    [id, patientId]
  );

  const onDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setDragging(false);
      const file = e.dataTransfer.files[0];
      if (file && file.type === 'application/pdf') {
        handleSubmit(file);
      } else {
        setSuccessMessage('❌ Only PDF files are accepted');
        setTimeout(() => setSuccessMessage(''), 5000);
      }
    },
    [handleSubmit]
  );

  const onDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (!dragging) setDragging(true);
  };

  const onDragLeave = () => {
    setDragging(false);
  };

  return (
    <div
      onDragOver={onDragOver}
      onDragLeave={onDragLeave}
      onDrop={onDrop}
      className={clsx('relative', dragging && 'ring-2 ring-blue-500 ring-offset-2')}
    >
      {dragging && (
        <div className="absolute inset-0 z-50 flex items-center justify-center bg-white/80 backdrop-blur-sm border-4 border-dashed border-blue-400 text-blue-600 text-xl font-semibold pointer-events-none">
          Drop PDF file to upload
        </div>
      )}

      {successMessage && (
        <div className="absolute top-4 right-4 z-50 rounded bg-white px-4 py-2 shadow-lg border">
          {successMessage}
        </div>
      )}

      {confirmOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="w-full max-w-md rounded-lg bg-white p-6 shadow-lg">
            <h2 className="text-lg font-semibold text-neutral-900 mb-3">Confirm Overwrite</h2>
            <p className="text-neutral-700 mb-5">{confirmMessage}</p>
            <div className="flex justify-end gap-3">
              <button
                onClick={() => {
                  setConfirmOpen(false);
                  setPendingFile(null);
                  setSuccessMessage('❌ Upload cancelled due to mismatch');
                }}
                className="px-4 py-2 text-sm rounded border border-neutral-300 hover:bg-neutral-100"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  setConfirmOpen(false);
                  if (pendingFile) handleSubmit(pendingFile, true);
                }}
                className="px-4 py-2 text-sm text-white bg-blue-600 hover:bg-blue-700 rounded"
              >
                Overwrite
              </button>
            </div>
          </div>
        </div>
      )}

      <ReportPatientInfo testSession={rftStore.testSession} />
      <Tabs
        defaultValue="results"
        onValueChange={(value) => setIsTrendViewActive(value === 'trend-view')}
      >
        <div className="mb-4 flex items-center gap-x-5">
          <RftStudyToolbar
            rftStore={rftStore}
            onUndoUpload={handleUndoUpload}
            pdfUploaded={pdfUploaded}
            originalData={originalData}
            undoingUpload={undoingUpload}
          />


          <div className="flex-1" />

           {/* File Attachment Dropdown Button */}
          <div className="relative">
            <Button
              variant="outlined"
              size="small"
              onPress={async () => {
                setShowFileMenu((prev) => !prev);
                if (!attachments.length) await fetchAttachmentList();
              }}
            >
              📎 File Attachment
            </Button>

            {showFileMenu && (
              <div className="absolute right-0 mt-2 w-72 rounded-xl bg-white shadow-xl ring-1 ring-neutral-200 z-50 max-h-96 overflow-auto animate-fade-in">
                <div className="py-2 text-sm text-neutral-800">
                  {loadingFiles ? (
                    <div className="px-4 py-3 text-neutral-500">Loading attachments...</div>
                  ) : attachments.length === 0 ? (
                    <div className="px-4 py-3 text-neutral-500">No attachments found</div>
                  ) : (
                    attachments.map((file) => (
                      <button
                        key={file.s3_key}
                        onClick={async () => {
                          try {
                            const res = await fetch("/api/attachments/download", {
                              method: "POST",
                              headers: { "Content-Type": "application/json" },
                              body: JSON.stringify({ key: file.s3_key ?? file.key })
                            });
                            const result = await res.json() as { url?: string };
                            if (result.url) {
                              window.open(result.url, "_blank");
                              setShowFileMenu(false);
                            }


                          } catch (err) {
                            console.error("Download failed", err);
                          }
                        }}
                        className="group w-full px-4 py-2 flex items-center gap-2 hover:bg-blue-50 transition-colors"
                      >
                        <svg
                          className="w-4 h-4 text-blue-500 group-hover:text-blue-600"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          viewBox="0 0 24 24"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" d="M4 16v2a2 2 0 002 2h12a2 2 0 002-2v-2M12 12v8m0 0l-4-4m4 4l4-4M4 4h16" />
                        </svg>
                        <span className="truncate">{file.filename}</span>
                      </button>
                    ))

                  )}
                </div>
                {/* Hidden file input */}
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".pdf"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      handleUploadAttachment(file);
                      e.target.value = ''; // reset input so same file can be picked again
                    }
                  }}
                  className="hidden"
                />
                <div className="border-t border-neutral-200 px-4 py-2">
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    className="w-full px-3 py-2 text-sm font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-md transition-colors"
                  >
                    + Upload Attachment
                  </button>
                </div>
              </div>
            )}

          </div>

          <Button
            variant="outlined"
            size="small"
            isDisabled={isLoading}
            isPending={isLoading}
            onPress={async () => {

              const spZscoreEl = document.getElementById('sp-zscore-plot');
              const coZscoreEl = document.getElementById('co-zscore-plot');
              const lvZscoreEl = document.getElementById('lv-zscore-plot');

              const spZscoreImage = spZscoreEl ? <Image style={tw('w-[140px]')} src={await toPng(spZscoreEl)} /> : null;
              const coZscoreImage = coZscoreEl ? <Image style={tw('w-[140px]')} src={await toPng(coZscoreEl)} /> : null;
              const lvZscoreImage = lvZscoreEl ? <Image style={tw('w-[140px]')} src={await toPng(lvZscoreEl)} /> : null;

              createReport({rftStore, spZscoreImage, coZscoreImage, lvZscoreImage}).then((url) => {
                window.open(url);
              });
            }}
          >
            <PaperIcon />
            {isLoading ? 'Compiling Report...' : 'View Report'}
          </Button>
        </div>

        <div className="flex items-start gap-x-3">
          <div
            className={clsx(
              'study-results w-278 shrink-0',
              isTrendViewActive ? 'sticky top-13.5 isolate z-10' : ''
            )}
          >
            <Suspense fallback={<div>Loading...</div>}>
              <TabsContent className="mt-0" value="results">
                <TestSections rftStore={rftStore} />
              </TabsContent>
              <TabsContent className="mt-0" value="trend-view">
                <PatientTrendTable />
              </TabsContent>
            </Suspense>
          </div>
          <ReportSidebar rftStore={rftStore} />
        </div>
      </Tabs>
    </div>
  );
});

const RftStudyToolbar = observer(({
  rftStore,
  onUndoUpload,
  pdfUploaded,
  originalData,
  undoingUpload
}: {
  rftStore: RftStore;
  onUndoUpload: () => void;
  pdfUploaded: boolean;
  originalData: any;
  undoingUpload: boolean;
}) => {

  const location = useLocation();
  const navigate = useNavigate();
  const [showModal, setShowModal] = useState(false);
  const [tempHeight, setTempHeight] = useState('');
  const [tempWeight, setTempWeight] = useState('');
  const [updateSessionHeight_] = useMutation(updateSessionHeight);
  const [updateSessionWeight_] = useMutation(updateSessionWeight);
  const [updateGender_] = useMutation(updatePatientGender);
  const [updateEthnicity_] = useMutation(updatePatientEthnicity);
  const { data: gendersData } = useQuery(getRFTGenders);
  const { data: ethnicitiesData } = useQuery(getEthnicities);
  const ethnicityOptions = ethnicitiesData?.pred_ref_ethnicities || [];
  const genderOptions = gendersData?.pred_ref_genders || [];
  const [tempGender, setTempGender] = useState('');
  const [tempEthnicity, setTempEthnicity] = useState('');
  const [missingHeight, setMissingHeight] = useState(false);
  const [missingWeight, setMissingWeight] = useState(false);
  const [missingGender, setMissingGender] = useState(false);
  const [missingEthnicity, setMissingEthnicity] = useState(false);
  const [hasEvaluatedPatientData, setHasEvaluatedPatientData] = useState(false);

  useEffect(() => {
    // Don't run again if already checked
    if (hasEvaluatedPatientData || !rftStore.isEditing || !rftStore.isHydrated) return;

    const searchParams = new URLSearchParams(location.search);
    const session = rftStore.testSession;
    const patient = session?.patient;

    if (!session || !patient) return;

    searchParams.set('edit', 'true');

    const height = session.height?.toString().trim();
    const weight = session.weight?.toString().trim();
    const ethnicity = patient.race_forrfts_code?.toString().trim();
    const gender = patient.gender_forrfts_code?.toString().trim();

    const isMissingHeight = !height || height.toLowerCase() === 'nan';
    const isMissingWeight = !weight || weight.toLowerCase() === 'nan';
    const isMissingGender = !gender || gender.toLowerCase() === 'nan';
    const isMissingEthnicity = !ethnicity || ethnicity.toLowerCase() === 'nan';

    // Trigger modal and set fields
    if (isMissingHeight || isMissingWeight || isMissingGender || isMissingEthnicity) {
      setTempHeight(isMissingHeight ? '' : height);
      setMissingHeight(isMissingHeight);

      setTempWeight(isMissingWeight ? '' : weight);
      setMissingWeight(isMissingWeight);

      setTempGender(isMissingGender ? '' : gender);
      setMissingGender(isMissingGender);

      setTempEthnicity(isMissingEthnicity ? '' : ethnicity);
      setMissingEthnicity(isMissingEthnicity);

      setShowModal(true);
    }
    navigate(
      {
        pathname: location.pathname,
        search: searchParams.toString(),
      },
      {replace: true}
    );

    setHasEvaluatedPatientData(true);
  }, [rftStore.isEditing, rftStore.isHydrated, hasEvaluatedPatientData]);

  useEffect(() => {
    if (rftStore.isEditing) {
      setHasEvaluatedPatientData(false); // Force recheck when toggling into edit mode
    }
  }, [rftStore.isEditing]);

  return (
    <>
      {showModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30">
          <div className="bg-white p-6 rounded shadow-xl w-full max-w-sm">
            <h2 className="text-lg font-semibold mb-4">Enter Patient RFT Info</h2>
            <div className="space-y-4">
              {missingHeight && (
                <div>
                  <label className="block text-sm mb-1">Height (cm)</label>
                  <input
                    type="number"
                    className="border w-full px-3 py-2 rounded text-sm"
                    value={tempHeight}
                    onChange={(e) => setTempHeight(e.target.value)}
                    placeholder="Height in cm"
                  />
                </div>
              )}

              {missingWeight && (
                <div>
                  <label className="block text-sm mb-1">Weight (kg)</label>
                  <input
                    type="number"
                    className="border w-full px-3 py-2 rounded text-sm"
                    value={tempWeight}
                    onChange={(e) => setTempWeight(e.target.value)}
                    placeholder="Weight in kg"
                  />
                </div>
              )}

              {missingGender && (
                <div>
                  <label className="block text-sm mb-1">Gender</label>
                  <select
                    value={tempGender}
                    onChange={(e) => setTempGender(e.target.value)}
                    className="border w-full px-3 py-2 rounded text-sm"
                  >
                    <option value="">Select Gender</option>
                    {genderOptions.map((g) => (
                      <option key={g.genderid} value={g.genderid}>
                        {g.description}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {missingEthnicity && (
                <div>
                  <label className="block text-sm mb-1">Ethnicity</label>
                  <select
                    value={tempEthnicity}
                    onChange={(e) => setTempEthnicity(e.target.value)}
                    className="border w-full px-3 py-2 rounded text-sm"
                  >
                    <option value="">Select Ethnicity</option>
                    {ethnicityOptions.map((e) => (
                      <option key={e.ethnicityid} value={e.ethnicityid}>
                        {e.description}
                      </option>
                    ))}
                  </select>
                </div>
              )}

            </div>

            <div className="flex justify-end gap-2 mt-6">
              <Button size="small" onPress={() => setShowModal(false)}>Cancel</Button>
              <Button
                size="small"
                onPress={async () => {
                  const parsedHeight = parseFloat(tempHeight);
                  const parsedWeight = parseFloat(tempWeight);

                  if (!isNaN(parsedHeight)) {
                    rftStore.testSession.height =  String(parsedHeight);
                    try {
                      await updateSessionHeight_({
                        variables: {
                          sessionId: rftStore.testSession?.sessionid,
                          height: String(parsedHeight),
                        },
                      });
                    } catch (err) {
                      console.error(err);
                      alert('❌ Height update failed');
                    }
                  }

                  if (!isNaN(parsedWeight)) {
                    rftStore.testSession.weight = String(parsedWeight);
                    try {
                      await updateSessionWeight_({
                        variables: {
                          sessionId: rftStore.testSession?.sessionid,
                          weight: String(parsedWeight),
                        },
                      });
                    } catch (err) {
                      console.error(err);
                      alert('❌ Weight update failed');
                    }
                  }
                  if (tempGender) {
                    rftStore.testSession.patient.gender_forrfts_code = tempGender;
                    try {
                      await updateGender_({
                        variables: {
                          patientId: rftStore.testSession?.patient?.patientid,
                          genderCode: tempGender,
                        },
                      });
                    } catch (err) {
                      console.error(err);
                      alert('❌ Gender update failed');
                    }
                  }
                  if (tempEthnicity) {
                    rftStore.testSession.patient.race_forrfts_code = tempEthnicity;
                    try {
                      await updateEthnicity_({
                        variables: {
                          patientId: rftStore.testSession?.patient?.patientid,
                          raceCode: tempEthnicity,
                        },
                      });
                    } catch (err) {
                      console.error(err);
                      alert('❌ Gender update failed');
                    }
                  }
                  setShowModal(false);
                }}
              >
                Save
              </Button>
            </div>
          </div>
        </div>
      )}

      <TabsList className="flex w-fit justify-start">
        <TabsTrigger
          className="flex cursor-pointer gap-x-2 font-normal text-neutral-700 data-[state=active]:font-medium data-[state=active]:text-neutral-800"
          value="results"
        >
          <SearchChatIcon className="size-4.5" />
          Results
        </TabsTrigger>
        <TabsTrigger
          className="flex cursor-pointer gap-x-2 font-normal text-neutral-700 data-[state=active]:font-medium data-[state=active]:text-neutral-800"
          value="trend-view"
        >
          <TrendDownGraphIcon className="size-4.5" />
          Trend View
        </TabsTrigger>
      </TabsList>

      <Tooltip>
        <TooltipTrigger asChild>
          <div>
            <Switch
              isSelected={rftStore.isEditing}
              onChange={(isSelected) =>
                rftStore.setProperty('isEditing', isSelected)
              }
              isDisabled={!rftStore.isReportEditable}
              isReadOnly={!rftStore.isReportEditable}
            >
              <div>Edit Mode</div>
              <div className="flex h-7.5 items-center gap-x-2 rounded border border-neutral-300 px-2">
                <div className="w-5 text-center text-[10px] text-neutral-600">
                  <div className="block [[data-selected]_&]:hidden">OFF</div>
                  <div className="hidden [[data-selected]_&]:block">ON</div>
                </div>
                <div className="indicator" />
              </div>
            </Switch>
          </div>
        </TooltipTrigger>
        {!rftStore.isReportEditable && (
          <TooltipContent>
            You cannot edit a completed report. Try amending instead.
          </TooltipContent>
        )}
      </Tooltip>

      {rftStore.isEditing && (
        <div className="flex items-center gap-x-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <button
                disabled={!undoStack.canUndo}
                onClick={() => undoStack.undo()}
                className={clsx(
                  'text-brand-600 cursor-pointer disabled:text-neutral-500',
                  !undoStack.canUndo && 'cursor-default'
                )}
              >
                <Undo2 className="size-4.5" />
              </button>
            </TooltipTrigger>
            <TooltipContent>Undo</TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <button
                disabled={!undoStack.canRedo}
                onClick={() => undoStack.redo()}
                className={clsx(
                  'text-brand-600 cursor-pointer disabled:text-neutral-500',
                  !undoStack.canRedo && 'cursor-default'
                )}
              >
                <Redo2 className="size-4.5" />
              </button>
            </TooltipTrigger>
            <TooltipContent>Redo</TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger asChild>
              <button
                disabled={!pdfUploaded || !originalData || undoingUpload}
                onClick={onUndoUpload}
                className={clsx(
                'cursor-pointer text-brand-600 disabled:text-neutral-500',
                (!pdfUploaded || !originalData || undoingUpload) && 'cursor-default' && 'hidden'
                )}
              >
              <Undo2 className="size-4.5" />
              </button>
            </TooltipTrigger>
            <TooltipContent>Undo PDF Import</TooltipContent>
          </Tooltip>
        </div>
      )}
    </>
  );
});

export default RFTStudyDetail;