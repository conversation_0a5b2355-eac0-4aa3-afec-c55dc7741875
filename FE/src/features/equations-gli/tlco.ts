import {apolloClient} from '@/apollo-client.ts';
import {
  type EquationCalcFn,
  type EquationMetadata,
  OutOfRangeResult,
  type PredResult,
} from '@/features/equations-registry/types.ts';

import {GET_TLCO_SPLINES} from './queries.ts';
import {LMSSplines} from './types.ts';
import {processSplineData} from './utils.ts';

/**
 * Get splines for TLCO calculation
 */
export async function getTLCOSplines(paramName: string, predMetadata: EquationMetadata): Promise<LMSSplines> {
  // Map gender to single character for TLCO lookup
  const genderChar = predMetadata.gender_for_rfts.charAt(0);

  const {data} = await apolloClient.query({
    query: GET_TLCO_SPLINES,
    variables: {
      parameter: paramName,
      age: predMetadata.age_clip.toString(),
      gender: genderChar,
    },
    fetchPolicy: 'cache-first',
  });

  // Add target age to the data for use in interpolation
  const targetAge = predMetadata.age_clip;

  return processSplineData(data, predMetadata.age_clip_result, targetAge);
}

/**
 * Calculate TLCO predictions
 */
export const calculatePredictionsTLCO = (async (
  paramName: string,
  predMetadata: EquationMetadata,
  testResult?: number
): Promise<PredResult | undefined> => {
  // todo: verify this
  if (paramName === 'TLCO(Hb)') paramName = 'TLCO';
  if (paramName === 'KCO(Hb)') paramName = 'KCO';

  // Get splines
  const splines = await getTLCOSplines(paramName, predMetadata);

  // Check if we can calculate based on input values
  if (
    (splines.LSpline === 0 && splines.MSpline === 0 && splines.SSpline === 0) ||
    predMetadata.Htcm_clip_result === OutOfRangeResult.high_no_clipmethod ||
    predMetadata.Htcm_clip_result === OutOfRangeResult.low_no_clipmethod
  ) {
    return;
  }

  // Initialize M, S, L values
  let M = 0;
  let S = 0;
  let L = 0;

  // Calculate based on gender and parameter
  const gender = predMetadata.gender_for_rfts.toLowerCase();

  if (gender === 'male') {
    switch (paramName.toLowerCase()) {
      case 'tlco':
        M = Math.exp(
          -7.03492 +
            2.018368 * Math.log(predMetadata.Htcm_clip) -
            0.012425 * Math.log(predMetadata.age_clip) +
            splines.MSpline
        );
        S = Math.exp(-1.98996 + 0.03536 * Math.log(predMetadata.age_clip) + splines.SSpline);
        L = 0.39482;
        break;
      case 'kco':
        M = Math.exp(
          4.088408 -
            0.415334 * Math.log(predMetadata.Htcm_clip) -
            0.113166 * Math.log(predMetadata.age_clip) +
            splines.MSpline
        );
        S = Math.exp(-1.98186 + 0.0146 * Math.log(predMetadata.age_clip) + splines.SSpline);
        L = 0.6733;
        break;
      case 'va':
        M = Math.exp(
          -11.086573 +
            2.430021 * Math.log(predMetadata.Htcm_clip) +
            0.097047 * Math.log(predMetadata.age_clip) +
            splines.MSpline
        );
        S = Math.exp(-2.20953 + 0.01937 * Math.log(predMetadata.age_clip) + splines.SSpline);
        L = 0.62559;
        break;
    }
  } else {
    // female
    switch (paramName.toLowerCase()) {
      case 'tlco':
        M = Math.exp(
          -5.159451 +
            1.618697 * Math.log(predMetadata.Htcm_clip) -
            0.01539 * Math.log(predMetadata.age_clip) +
            splines.MSpline
        );
        S = Math.exp(-1.82905 - 0.01815 * Math.log(predMetadata.age_clip) + splines.SSpline);
        L = 0.2416;
        break;
      case 'kco':
        M = Math.exp(
          5.131492 -
            0.645656 * Math.log(predMetadata.Htcm_clip) -
            0.097395 * Math.log(predMetadata.age_clip) +
            splines.MSpline
        );
        S = Math.exp(-1.63787 - 0.07757 * Math.log(predMetadata.age_clip) + splines.SSpline);
        L = 0.48963;
        break;
      case 'va':
        M = Math.exp(
          -9.87397 +
            2.182316 * Math.log(predMetadata.Htcm_clip) +
            0.082868 * Math.log(predMetadata.age_clip) +
            splines.MSpline
        );
        S = Math.exp(-2.08839 - 0.01334 * Math.log(predMetadata.age_clip) + splines.SSpline);
        L = 0.51919;
        break;
    }
  }

  // Calculate LLN and ULN
  const lln = Math.exp(Math.log(M) + Math.log(1 - 1.645 * L * S) / L);
  const uln = Math.exp(Math.log(M) + Math.log(1 + 1.645 * L * S) / L);

  // Prepare results
  const results: PredResult = {
    mpv: M,
    lln: lln,
    uln: uln,
  };

  // Calculate Z-score if test result provided
  if (testResult !== undefined && testResult > 0) {
    results.zscore = ((testResult / M) ** L - 1) / (L * S);
  }

  if (results.zscore === Infinity) results.zscore = undefined;
  if (results.mpv === Infinity) results.zscore = undefined;
  if (results.lln === Infinity) results.zscore = undefined;
  if (results.uln === Infinity) results.zscore = undefined;

  return results;
}) satisfies EquationCalcFn;
