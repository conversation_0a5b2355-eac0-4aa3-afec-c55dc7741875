import {apolloClient} from '@/apollo-client.ts';
import {
  type EquationCalcFn,
  type EquationMetadata,
  OutOfRangeResult,
  type PredResult,
} from '@/features/equations-registry/types.ts';

import {GET_LV_SPLINES} from './queries.ts';
import {LMSSplines} from './types.ts';
import {processSplineData} from './utils.ts';

/**
 * Get splines for Lung Volumes calculation
 */
export async function getLVSplines(paramName: string, predMetadata: EquationMetadata): Promise<LMSSplines> {
  // Map parameter names
  if (paramName === 'RV/TLC') paramName = 'RVTLC';
  if (paramName === 'LvVC') paramName = 'VC';

  // Map gender to single character for LV lookup
  const genderChar = predMetadata.gender_for_rfts.charAt(0);

  const {data} = await apolloClient.query({
    query: GET_LV_SPLINES,
    variables: {
      parameter: paramName,
      age: predMetadata.age_clip.toString(),
      gender: genderChar,
    },
    fetchPolicy: 'cache-first',
  });

  // Add target age to the data for use in interpolation
  const targetAge = predMetadata.age_clip;

  return processSplineData(data, predMetadata.age_clip_result, targetAge);
}

/**
 * Calculate Lung Volumes predictions
 */
export const calculatePredictionsLV = async function (
  paramName: string,
  predMetadata: EquationMetadata,
  testResult?: number
): Promise<PredResult | undefined> {
  // Get splines
  const splines = await getLVSplines(paramName, predMetadata);

  // Check if we can calculate based on input values
  if (
    (splines.LSpline === 0 && splines.MSpline === 0 && splines.SSpline === 0) ||
    predMetadata.Htcm_clip_result === OutOfRangeResult.high_no_clipmethod ||
    predMetadata.Htcm_clip_result === OutOfRangeResult.low_no_clipmethod
  ) {
    return;
  }

  // Initialize M, S, L values
  let M = 0;
  let S = 0;
  let L = 0;

  // Calculate based on gender and parameter
  const gender = predMetadata.gender_for_rfts.toLowerCase();
  const age = predMetadata.age_clip;
  const height = predMetadata.Htcm_clip;

  if (gender === 'male') {
    switch (paramName.toLowerCase()) {
      case 'frc':
        M = Math.exp(-13.4898 + 0.1111 * Math.log(age) + 2.7634 * Math.log(height) + splines.MSpline);
        S = Math.exp(-1.60197 + 0.01513 * Math.log(age) + splines.SSpline);
        L = 0.3416;
        break;
      case 'tlc':
        M = Math.exp(-10.5861 + 0.1433 * Math.log(age) + 2.3155 * Math.log(height) + splines.MSpline);
        S = Math.exp(-2.0616143 - 0.0008534 * age + splines.SSpline);
        L = 0.9337;
        break;
      case 'rv':
        M = Math.exp(-2.37211 + 0.01346 * age + 0.01307 * height + splines.MSpline);
        S = Math.exp(-0.878572 - 0.007032 * age + splines.SSpline);
        L = 0.5931;
        break;
      case 'rv/tlc':
        M = Math.exp(2.634 + 0.01302 * age - 0.00008862 * height + splines.MSpline);
        S = Math.exp(-0.96804 - 0.01004 * age + splines.SSpline);
        L = 0.8646;
        break;
      case 'erv':
        M = Math.exp(-17.32865 - 0.006288 * age + 3.478116 * Math.log(height) + splines.MSpline);
        S = Math.exp(-1.307616 + 0.009177 * age);
        L = 0.5517;
        break;
      case 'ic':
        M = Math.exp(-10.121688 + 0.001265 * age + 2.188801 * Math.log(height) + splines.MSpline);
        S = Math.exp(-1.856546 + 0.002008 * age);
        L = 1.146;
        break;
      case 'lvvc':
      case 'vc':
      case 'svc':
        M = Math.exp(-10.134371 - 0.003532 * age + 2.30798 * Math.log(height) + splines.MSpline);
        S = Math.exp(-2.1367411 + 0.0009367 * age);
        L = 0.8611;
        break;
    }
  } else {
    // female
    switch (paramName.toLowerCase()) {
      case 'frc':
        M = Math.exp(-12.7674 + 0.1251 * Math.log(age) + 2.6049 * Math.log(height) + splines.MSpline);
        S = Math.exp(-1.4831 - 0.03372 * Math.log(age) + splines.SSpline);
        L = 0.2898;
        break;
      case 'tlc':
        M = Math.exp(-10.1128 + 0.1062 * Math.log(age) + 2.2259 * Math.log(height) + splines.MSpline);
        S = Math.exp(-2.0999321 + 0.0001564 * age + splines.SSpline);
        L = 0.4636;
        break;
      case 'rv':
        M = Math.exp(-2.50593 + 0.01307 * age + 0.01379 * height + splines.MSpline);
        S = Math.exp(-0.90255 - 0.006005 * age + splines.SSpline);
        L = 0.4197;
        break;
      case 'rv/tlc':
        M = Math.exp(2.666 + 0.01411 * age - 0.00003689 * height + splines.MSpline);
        S = Math.exp(-0.976602 - 0.009679 * age + splines.SSpline);
        L = 0.8037;
        break;
      case 'erv':
        M = Math.exp(-14.145513 - 0.009573 * age + 2.871446 * Math.log(height) + splines.MSpline);
        S = Math.exp(-1.54992 + 0.01409 * age);
        L = 0.5326;
        break;
      case 'ic':
        M = Math.exp(-9.4438787 - 0.0002484 * age + 2.0312769 * Math.log(height) + splines.MSpline);
        S = Math.exp(-1.775276 + 0.002673 * age);
        L = 0.9726;
        break;
      case 'lvvc':
      case 'vc':
      case 'svc':
        M = Math.exp(-9.2306 - 0.005517 * age + 2.116822 * Math.log(height) + splines.MSpline);
        S = Math.exp(-2.22026 + 0.002956 * age);
        L = 1.038;
        break;
    }
  }

  // Calculate LLN and ULN
  const lln = Math.exp(Math.log(M) + Math.log(1 - 1.645 * L * S) / L);
  const uln = Math.exp(Math.log(M) + Math.log(1 + 1.645 * L * S) / L);

  // Prepare results
  const results: PredResult = {
    mpv: M,
    lln,
    uln,
  };

  // Calculate Z-score if test result provided
  if (testResult !== undefined && testResult > 0) {
    results.zscore = ((testResult / M) ** L - 1) / (L * S);
  }

  if (results.zscore === Infinity) results.zscore = undefined;
  if (results.mpv === Infinity) results.zscore = undefined;
  if (results.lln === Infinity) results.zscore = undefined;
  if (results.uln === Infinity) results.zscore = undefined;

  return results;
} satisfies EquationCalcFn;
