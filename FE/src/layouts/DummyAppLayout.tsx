/*
 * Only used for dummy app shell behind register screen
 */
import { type PropsWithChildren, createElement } from 'react';

import CallingIconly from '@/assets/iconly/Calling.svg?react';
import ClipboardMenuIconly from '@/assets/iconly/ClipboardMenu.svg?react';
import DatabaseIconly from '@/assets/iconly/DataBase.svg?react';
import DeliverySearchIconly from '@/assets/iconly/DeliverySearch.svg?react';
import DocumentCopyIconly from '@/assets/iconly/DocumentCopy.svg?react';
import GridInterfaceIconly from '@/assets/iconly/GridInterface.svg?react';
import SparksAi from '@/assets/iconly/SparksAi.svg?react';
import UserSearchIconly from '@/assets/iconly/UserSearch.svg?react';
import { Breadcrumb, BreadcrumbItem, BreadcrumbList, BreadcrumbPage } from '@/components/ui/breadcrumb';
import { Sidebar, SidebarInset, SidebarMenu, SidebarMenuButton, SidebarMenuItem, SidebarProvider, SidebarRail } from '@/components/ui/sidebar';

const sidebarItems = [
  {
    name: 'Dashboard',
    icon: GridInterfaceIconly,
  },
  {
    name: 'Booking',
    icon: ClipboardMenuIconly,
  },
  {
    name: 'Patients',
    icon: UserSearchIconly,
  },
  {
    name: 'Reporting',
    icon: DocumentCopyIconly,
  },
  {
    name: 'Contacts',
    icon: CallingIconly,
  },
  {
    name: 'Data',
    icon: DatabaseIconly,
  },
  {
    name: 'Quality Control',
    icon: DeliverySearchIconly,
  },
  {
    name: 'Magic Import',
    icon: SparksAi,
  },
] as const;

export function DummyAppLayout({children}: PropsWithChildren) {
  return (
    <SidebarProvider>
      <Sidebar collapsible="icon">
        <div
          data-sidebar="header"
          className="bg-brand-900 flex h-[88px] flex-col gap-2 p-5 pb-4.5"
        >
          <img
            className="w-28"
            alt="rezibase-logo"
            src="/logo-white.png"
          />
        </div>

        <div
          data-sidebar="content"
          className="bg-brand-900 flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden"
        >
          <SidebarMenu>
            {sidebarItems?.map((item) => (
              <SidebarMenuItem key={item.name}>
                <SidebarMenuButton
                  className="relative"
                  tooltip={item.name}
                >
                  <div className="[.item-active_&]:bg-brand2-400 absolute inset-y-0 left-0 w-1" />
                  {createElement(item.icon)}
                  <span className="text-xs font-semibold uppercase">{item.name}</span>
                </SidebarMenuButton>
              </SidebarMenuItem>
            ))}
          </SidebarMenu>
        </div>
        <SidebarRail />
      </Sidebar>

      <SidebarInset>
        <header className="sticky top-0 z-10 flex h-16 items-center justify-between gap-2 border-b bg-white px-6">
          <div className="flex items-center gap-2">
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbPage>Patient</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <div className="flex flex-1 flex-col gap-4 bg-neutral-50 px-6 pt-6 pb-4">{children}</div>
      </SidebarInset>
    </SidebarProvider>
  );
}
