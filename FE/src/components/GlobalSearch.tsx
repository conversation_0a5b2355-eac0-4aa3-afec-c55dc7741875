import clsx from 'clsx';
import {ComponentProps, useEffect, useMemo, useState} from 'react';
import {
  Autocomplete,
  Button,
  Dialog,
  DialogTrigger,
  Input,
  Menu,
  MenuItem,
  Modal,
  ModalOverlay,
  TextField,
} from 'react-aria-components';

import {useQuery} from '@apollo/client';
import {SearchIcon} from 'lucide-react';
import {observer} from 'mobx-react-lite';

import {apolloClient} from '@/apollo-client.ts';
import AvatarIcon from '@/components/icons/AvatarIcon.tsx';
import {searchPatientsQuery} from '@/graphql/patients.ts';
import {GET_SIDEBAR_MODULES} from '@/graphql/sidebar.ts';
import {Patient} from '@/store/patient';
import authStore from '@/store/auth.store.ts';

export const GlobalSearch = observer((props: ComponentProps<typeof Button>) => {
  const {data} = useQuery(GET_SIDEBAR_MODULES);
  const siteId = authStore.tokenSiteId;

  const [searchText, setSearchText] = useState('');
  const [patients, setPatients] = useState<Patient[]>([]);
  const [_, setIsLoading] = useState(false);

  let [isOpen, setOpen] = useState(false);
  let isMac = useMemo(() => /Mac/.test(navigator.platform), []);

  const searchPatients = async (text: string) => {
    if (!siteId) {
      setPatients([]);
      return;
    }

    setIsLoading(true);
    try {
      const {data} = await apolloClient.query({
        query: searchPatientsQuery,
        variables: {
          searchText: text,
          genderCodes: null,
          siteId: String(siteId),
          limit: 20,
          offset: 0,
        },
        fetchPolicy: 'network-only',
      });

      setPatients(data.search_pas_pt.map((p) => new Patient(p as any, false)));
    } catch (error) {
      console.error('Search failed:', error);
      setPatients([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      searchPatients(searchText);
    }, 300); // Debounce search

    return () => clearTimeout(timeoutId);
  }, [searchText, siteId]);

  // Clear search when site changes
  useEffect(() => {
    setSearchText('');
    setPatients([]);
  }, [siteId]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'k' && (isMac ? e.metaKey : e.ctrlKey)) {
        e.preventDefault();
        setOpen((prev) => !prev);
      } else if (e.key === 'Escape') {
        e.preventDefault();
        setOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  });

  if (!data?.sidebar_modules?.some((e) => e.title.toLowerCase() === 'patients')) {
    return null;
  }

  return (
    <DialogTrigger
      isOpen={isOpen}
      onOpenChange={setOpen}
    >
      <Button
        {...props}
        className={clsx(
          props.className,
          'focus-visible:border-brand2-500 focus-visible:ring-brand2-400/30 flex h-8 cursor-pointer items-center gap-x-2 rounded-sm border border-neutral-300 bg-white px-2 py-2 text-left text-sm text-neutral-500 transition hover:border-neutral-400 hover:bg-gray-50 focus:outline-hidden focus-visible:ring-2'
        )}
      >
        <SearchIcon className="size-4.5" />
        Search Patients...
      </Button>
      <ModalOverlay
        isDismissable
      >
        <Modal className="react-aria-Modal w-[560px] p-0">
          <Dialog className="react-aria-Dialog">
            <Autocomplete
              inputValue={searchText}
              onInputChange={setSearchText}
            >
              <TextField
                aria-label="Patients search"
                className="relative m-4 flex items-center rounded-sm border border-neutral-300 pl-8.5"
              >
                <SearchIcon className="pointer-events-none absolute left-2 size-4.5 text-neutral-400" />
                <Input
                  autoFocus
                  placeholder="Search Patients"
                  className="flex h-10 w-full rounded-sm bg-transparent py-2.25 text-sm outline-none placeholder:text-neutral-600 disabled:cursor-not-allowed disabled:opacity-50"
                />
              </TextField>
              <Menu
                items={patients}
                className="max-h-[450px] overflow-x-hidden overflow-y-auto"
              >
                {(item) => (
                  <MenuItem
                    className="data-[selected=true]:text-accent-foreground relative flex cursor-pointer items-center gap-x-3 rounded-sm p-4 text-sm outline-none select-none data-focused:bg-neutral-100 data-hovered:bg-neutral-100"
                    textValue={item.fullName}
                    href={`/patients/${item.patientid}`}
                    id={item.patientid}
                  >
                    <div className="bg-brand-100 flex h-10 w-10 items-center justify-center rounded-full text-xl text-white">
                      <AvatarIcon className="h-[22.43px] w-[18.35px]" />
                    </div>
                    <div className="space-y-1">
                      <div className="text-sm font-semibold text-neutral-900">{item.fullName}</div>
                      <div className="flex items-center gap-x-1.5 text-xs text-neutral-700">
                        {item.ur && (
                          <div className="flex items-center gap-x-1.5">
                            <span>{item.ur}</span>
                            <div className="h-0.75 w-0.75 rounded-full bg-neutral-500" />
                          </div>
                        )}
                        {[item.genderResolved?.description, item.ageToday].filter(Boolean).join(', ')}
                      </div>
                    </div>
                  </MenuItem>
                )}
              </Menu>
            </Autocomplete>
          </Dialog>
        </Modal>
      </ModalOverlay>
    </DialogTrigger>
  );
});

export default GlobalSearch;
