import {RouterProvider, createBrowserRouter} from 'react-router';

import {loadDevMessages, loadErrorMessages} from '@apollo/client/dev';

import ModalStateProvider from '@/components/modal-state-provider.tsx';
import {routes} from '@/route.tsx';

loadDevMessages();
loadErrorMessages();

function App() {
  const router = createBrowserRouter(routes);

  return (
    <ModalStateProvider>
      <RouterProvider router={router} />
    </ModalStateProvider>
  );
}

export default App;
